# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a Vue 3 environment management admin system built with Vben Admin, a Vue enterprise management framework. The project uses a monorepo structure with PNPM and Turbo for efficient package management and builds.

## Architecture

### Monorepo Structure
- **apps/web-antd/**: Main Vue 3 application using Ant Design Vue
- **packages/**: Shared packages and utilities
- **internal/**: Internal tooling (lint configs, build utilities, etc.)
- **scripts/**: Build and utility scripts

### Main Application Structure (apps/web-antd/)
- **src/views/**: Page components organized by feature modules
  - `simulation/`: Equipment and environment modeling, scenario design
  - `exp-analyze/`: Experiment analysis and evaluation
  - `exp-run/`: Experiment execution and control
  - `modeling-tool/`: Data management and model configuration
  - `sys2/`: Evaluation indicators and algorithm management
- **src/components/**: Reusable components
- **src/api/**: API client organized by modules
- **src/store/**: Pinia stores for state management
- **src/router/**: Vue Router configuration

### Key Technologies
- Vue 3 with Composition API
- Ant Design Vue for UI components
- Vite for build tooling
- Pinia for state management
- TypeScript for type safety
- Cesium for 3D visualization
- ECharts for data visualization

## Common Commands

### Development
```bash
# Start development server for main app
pnpm dev:antd

# Start any app in development
pnpm dev

# Install dependencies
pnpm install
```

### Building
```bash
# Build all packages
pnpm build

# Build specific app
pnpm build:antd

# Build with bundle analysis
pnpm build:analyze
```

### Code Quality
```bash
# Run linting
pnpm lint

# Format code
pnpm format

# Type checking
pnpm check:type

# Run all checks (circular deps, types, spelling)
pnpm check
```

### Testing
```bash
# Run unit tests
pnpm test:unit

# Run E2E tests
pnpm test:e2e
```

## Development Workflow

### File Organization
- Component files use PascalCase naming
- API modules are organized by feature (core, module1-5, system)
- Views are grouped by functional modules
- Shared utilities in src/utils/

### API Integration
The application communicates with a C++ backend via HTTP API for:
- File operations (open, save, upload, download)
- Tool integration (GlobalMapper, Photoshop, TexView, etc.)
- Simulation and analysis operations
- Battle deployment and planning

### State Management
- Auth state in `src/store/auth.ts`
- Model import state in `src/store/modelImport.ts`
- WebSocket connections in `src/store/websocket.ts`

### Styling
- Global styles in `src/assets/styles/`
- Tailwind CSS for utility classes
- Custom fonts for Chinese text support
- **Style Modification Guidelines**:
  - 对于通用组件的样式修改，放到apps\web-antd\src\assets\styles\base.scss中进行全局样式的修改

## Important Notes

- Uses PNPM workspace with shared dependencies via catalog
- Turbo for optimized monorepo builds
- Node.js >=20.10.0 and PNPM >=9.12.0 required
- Environment variables configured via .env files
- WebSocket integration for real-time communication

## Additional Guidelines
- 必须用中文回复：在与项目相关的技术交流和文档编写中，优先使用中文语言进行沟通和描述