# 环境评估仿真系统 - HTTP API参考文档

## 概述

本文档详细描述了环境评估仿真系统提供的HTTP接口，包括请求格式、支持的操作和响应格式。该系统使用HTTP协议提供文件操作、工具启动和数据处理等功能。

## 基本信息

- **请求格式**: 支持POST方法和OPTIONS方法(用于CORS预检请求)，内容类型必须为`application/json`
- **编码方式**: UTF-8
- **响应格式**: JSON格式，内容类型为`application/json`
- **跨域支持**: 所有接口支持跨域访问 (CORS)，支持OPTIONS预检请求

## 跨域资源共享 (CORS)

系统支持跨域资源共享，允许从不同源的网页进行API调用。具体实现包括：

- 所有响应都包含`Access-Control-Allow-Origin: *`头，允许任何源的访问
- 支持OPTIONS预检请求，返回以下头信息：
  - `Access-Control-Allow-Methods: POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, Accept, Authorization`
  - `Access-Control-Max-Age: 86400` (预检请求结果缓存24小时)

## 请求格式

所有请求都必须是POST方法，并且需要包含以下JSON结构：

```json
{
  "action": "<操作类型>",
  "data": {
    // 操作特定的数据字段
  }
}
```

### 通用请求参数

在`data`对象中可以包含以下通用参数：

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID  | String | 用户标识符 | 否 |
| fileType | Number | 文件类型标识符 | 否 |

## 响应格式

所有响应都使用统一的JSON格式：

```json
{
  "action": "<操作类型>",
  "status": "<状态>",
  // 其他特定于操作的字段
}
```

### 响应状态

响应中的`status`字段可能包含以下值之一：

| 状态值 | 描述 |
|--------|------|
| success | 操作成功完成 |
| bad_request | 请求参数错误或不完整 |
| not_found | 请求的资源未找到 |
| unauthorized | 未经授权的访问请求 |
| error | 服务器内部错误 |
| canceled | 操作被用户取消 |

## 支持的操作

系统支持以下操作类型（通过请求中的`action`字段指定）：

### 1. 文件操作

#### 1.1 打开文件

**操作**: `openfile`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String | 用户标识符 | 否 |
| titile | String | 文件对话框标题 | 否 |
| dir | String | 默认打开目录 | 否 |
| filter | String | 文件过滤器 | 否 |
| fileType | Number | 文件类型 (1=地形，2=装备模型) | 否 |

**响应字段**:

响应包含一个`metadata`对象，其中包含以下字段:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| filepath | String | 文件所在目录路径 |
| filename | String | 文件名称 |
| parentpath | String | 父目录名称 |
| content | String | 文件内容（文本文件） |

**示例请求**:

```json
{
  "action": "openfile",
  "data": {
    "usrID": "user123",
    "titile": "请选择文件",
    "dir": "C:/Documents",
    "filter": "所有文件(*.*)"
  }
}
```

**示例响应**:

```json
{
  "action": "openfile",
  "status": "success", 
  "metadata": {
    "filepath": "C:/Documents/",
    "filename": "report.txt",
    "parentpath": "Documents",
    "content": "文件内容..."
  }
}
```

**示例响应(用户取消选择文件):**

```json
{
  "action": "openfile",
  "status": "canceled", 
  "metadata": {}
}
```

**注意**:
- 此接口支持两种参数格式：可以使用`dir`+`filter`或`defaultDir`+`fileType`
- 响应可能使用`metadata`对象包含文件信息，或直接在响应根级别包含这些字段
- 字段命名可能有变化，例如`filepath`/`path`、`parentpath`/`parentdir`、`content`/`connect`
- 当用户点击"取消"按钮或关闭文件选择对话框而未选择文件时，接口将返回status为"canceled"的响应

#### 1.2 保存文件

**操作**: `savefile`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String | 用户标识符 | 否 |
| filepath | String | 目标目录路径 | 是 |
| content | String | 文件内容 | 是 |
| fileType | Number | 文件类型 | 否 |

**响应字段**:

响应包含一个`metadata`对象，其中包含以下字段:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| path | String | 保存后的文件完整路径 |

**示例请求**:

```json
{
  "action": "savefile",
  "data": {
    "usrID": "user123",
    "filepath": "C:/Documents/",
    "content": "文件内容",
    "fileType": 1
  }
}
```

**示例响应**:

```json
{
  "action": "savefile",
  "status": "success",
  "metadata": {
    "path": "C:/Documents/config.cpp"
  }
}
```

**注意**:
- 该操作总是将内容保存到指定`filepath`路径下的`config.cpp`文件中
- 如果目录不存在，会自动创建目录
- 如果文件已存在，会覆盖其内容

#### 1.3 另存为文件

**操作**: `savefileas`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String | 用户标识符 | 否 |
| path | String | 源文件或文件夹路径 | 是 |
| type | String | 类型，"file"表示文件，"folder"表示文件夹 | 是 |
| fileType | Number | 文件类型 | 否 |

**响应字段**:

响应包含一个`metadata`对象，其中包含以下字段:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| newPath | String | 保存后的文件或文件夹完整路径 |

**示例请求**:

```json
{
  "action": "savefileas",
  "data": {
    "usrID": "user123",
    "path": "C:/Documents/测试数据",
    "type": "folder",
    "fileType": 1
  }
}
```

或

```json
{
  "action": "savefileas",
  "data": {
    "usrID": "user123",
    "path": "C:/Documents/测试数据/备注.txt",
    "type": "file",
    "fileType": 1
  }
}
```

**示例响应**:

```json
{
  "action": "savefileas",
  "status": "success",
  "metadata": {
    "newPath": "C:/Projects/Archive/测试数据"
  }
}
```

**注意**:
- 此操作会弹出系统原生的文件或文件夹选择对话框
- 选择目标位置后，将使用Windows的复制粘贴机制完成复制
- 如果目标位置已存在同名文件或文件夹，系统会提示是否覆盖

#### 1.4 路径检查

**操作**: `checkpath`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| path | String | 要检查的路径 | 是 |
| checkType | Number | 检查类型 (1=目录存在性，2=文件存在性) | 是 |

**响应字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| exists | Boolean | 路径是否存在 |
| isValid | Boolean | 路径是否有效 |

**示例请求**:

```json
{
  "action": "checkpath",
  "data": {
    "path": "C:/Projects/Terrain",
    "checkType": 1
  }
}
```

**示例响应**:

```json
{
  "action": "checkpath",
  "status": "success", 
  "exists": true,
  "isValid": true
}
```

#### 1.5 文件上传

**操作**: `upload`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| sourcePath | String | 源文件路径 | 是 |
| targetPath | String | 目标路径 | 是 |
| overwrite | Boolean | 是否覆盖已存在的文件 | 否 |

**响应字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| targetPath | String | 目标文件完整路径 |
| size | Number | 上传的文件大小（字节） |

**示例请求**:

```json
{
  "action": "upload",
  "data": {
    "sourcePath": "C:/LocalData/terrain.tif",
    "targetPath": "C:/Projects/Terrain/terrain.tif",
    "overwrite": true
  }
}
```

**示例响应**:

```json
{
  "action": "upload",
  "status": "success", 
  "targetPath": "C:/Projects/Terrain/terrain.tif",
  "size": 1048576
}
```

#### 1.6 文件下载

**操作**: `download`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| sourcePath | String | 源文件路径 | 是 |
| targetPath | String | 目标保存路径 | 是 |
| overwrite | Boolean | 是否覆盖已存在的文件 | 否 |

**响应字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| targetPath | String | 目标文件完整路径 |
| size | Number | 下载的文件大小（字节） |

**示例请求**:

```json
{
  "action": "download",
  "data": {
    "sourcePath": "C:/Projects/Terrain/terrain.tif",
    "targetPath": "C:/LocalData/terrain_download.tif",
    "overwrite": true
  }
}
```

**示例响应**:

```json
{
  "action": "download",
  "status": "success", 
  "targetPath": "C:/LocalData/terrain_download.tif",
  "size": 1048576
}
```
#### 1.7 数据打包

**操作**: `package`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String | 用户标识符 | 否 |
| sourcePath | String | 源数据路径 | 是 |
| targetPath | String | 目标打包路径 | 是 |
| configName | String | 打包配置名称 | 是 |
| fileType | Number | 文件类型 | 否 |
| configParams | Object | 额外配置参数 | 否 |

**响应字段**:

响应仅包含一个空的`metadata`对象，表示打包操作已开始。打包完成后会通过其他方式通知服务端。

**示例请求**:

```json
{
  "action": "package",
  "data": {
    "usrID": "user123",
    "sourcePath": "C:/Documents/SourceData",
    "targetPath": "C:/Archive/project1.zip",
    "configName": "standard",
    "fileType": 1,//1地形，2装备模型，0其他
    "configParams": {
      "param1": "high",
      "param2": true
    }
  }
}
```

**示例响应**:

```json
{
  "action": "package",
  "status": "success", 
  "metadata": {}
}
```

**注意**:
- 此操作是异步的，响应只表示打包操作已开始
- 打包任务在后台进行，完成后会通过其他方式通知服务端
- 打包操作可能需要较长时间，客户端不应等待响应后再继续其他操作

## 错误处理

当请求处理过程中发生错误时，响应将包含以下格式：

```json
{
  "action": "<请求的操作类型>",
  "status": "<错误状态>"
}
```
#### 1.X 选择目录

**操作**: `selectdirectory`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String | 用户标识符 | 否 |
| title | String | 对话框标题 | 否 |
| initialDir | String | 初始目录路径 | 否 |

**响应字段**:

响应包含一个`metadata`对象，其中包含以下字段:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| path | String | 用户选择的目录路径 |

**示例请求**:

```json
{
  "action": "selectdirectory",
  "data": {
    "usrID": "user123",
    "title": "请选择目录",
    "initialDir": "C:/Documents"
  }
}
```

**示例响应**:

```json
{
  "action": "selectdirectory",
  "status": "success",
  "metadata": {
    "path": "C:/Documents/所选目录"
  }
}
```

**示例响应(用户取消选择目录):**

```json
{
  "action": "selectdirectory",
  "status": "canceled", 
  "metadata": {}
}
```

**注意**:
- 此操作会弹出系统原生的目录选择对话框
- 如果用户取消选择，将返回status为"canceled"的响应
- 如果未指定initialDir，则默认使用用户主目录

### 2. 工具操作

#### 2.1 统一工具启动接口

**操作**: `openTheTool`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String | 用户标识符 | 否 |
| type | Number | 工具类型(1=GlobalMapper, 2=Photoshop, 3=TexView, 4=Oxygen, 5=Visitor4, 6=3DMAX) | 是 |

**响应字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| toolType | Number | 工具类型 |

**示例请求(GlobalMapper)**:

```json
{
  "action": "openTheTool",
  "data": {
    "usrID": "user123",
    "type": 1
  }
}
```

**示例请求(Photoshop)**:

```json
{
  "action": "openTheTool",
  "data": {
    "usrID": "user123",
    "type": 2
  }
}
```

**示例请求(Visitor4)**:

```json
{
  "action": "openTheTool",
  "data": {
    "usrID": "user123",
    "type": 5
  }
}
```

**示例响应**:

```json
{
  "action": "openTheTool",
  "status": "success", 
  "metadata": {
    "toolType": 1
  } 
}
```

**注意**:
- 旧版API中的独立工具接口(startglobalmapper, startphotoshop等)仍然保持兼容，但建议使用此统一接口
- 不同类型工具可能需要不同的参数，请参考各工具类型的具体要求
- type对应关系: 1=GlobalMapper, 2=Photoshop, 3=TexView, 4=Oxygen, 5=Visitor4, 6=3DMAX

### 3. 仿真与分析操作

#### 3.1 启动仿真校验

**操作**: `startsimulation`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| configPath | String | 配置文件路径 | 是 |
| resultPath | String | 结果输出路径 | 是 |
| parameters | Object | 仿真参数设置 | 否 |

**响应字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| taskId | String | 仿真任务标识符 |
| configPath | String | 使用的配置文件路径 |
| resultPath | String | 结果文件路径 |

**示例请求**:

```json
{
  "action": "startsimulation",
  "data": {
    "usrID":1,
    "modelName": "03式履带指挥车",
    "modelClass": "track_ZK_425"
  }
}
```

**示例响应**:

```json
{
  "action": "startsimulation",
  "status": "success", 
  "metadata": {
    "usrID":1,
    "modelName": "03式履带指挥车",
    "modelClass": "track_ZK_425"
  } 
}
```

#### 3.2 启动AI推演

**操作**: `startaiinference`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String/Number | 用户标识符 | 否 |
| transcribe | Boolean | 是否录制 | 否 |
| collectData | Boolean | 是否采集数据 | 否 |
| stepLength | Number | 步长(0.008-16) | 否 |
| LvR | Number | 红方等级 | 否 |
| ExpR | Number | 红方经验值 | 否 |
| LvB | Number | 蓝方等级 | 否 |
| ExpB | Number | 蓝方经验值 | 否 |

**响应字段**:

响应包含一个`metadata`对象，其中包含以下字段:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| transcribe | Boolean | 是否录制 |
| collectData | Boolean | 是否采集数据 |
| stepLength | Number | 步长 |
| LvR | Number | 红方等级 |
| ExpR | Number | 红方经验值 |
| LvB | Number | 蓝方等级 |
| ExpB | Number | 蓝方经验值 |

**示例请求**:

```json
{
  "action": "startaiinference",
  "data": {
    "usrID": 1,
    "transcribe": true,
    "collectData": true,
    "stepLength": 0.189,
    "LvR": 0.488,
    "ExpR": 0.199,
    "LvB": 0.488,
    "ExpB": 0.199
  }
}
```

**示例响应**:

```json
{
  "action": "startaiinference",
  "status": "success", 
  "metadata": {
    "transcribe": true,
    "collectData": true,
    "stepLength": 0.189,
    "LvR": 0.488,
    "ExpR": 0.199,
    "LvB": 0.488,
    "ExpB": 0.199
  } 
}
```

#### 3.3 启动人在环推演

**操作**: `starthumaninloop`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| scenarioPath | String | 场景文件路径 | 是 |
| outputPath | String | 输出路径 | 是 |
| participants | Array | 参与者信息 | 否 |

**响应字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| taskId | String | 任务标识符 |
| scenarioPath | String | 使用的场景文件 |
| outputPath | String | 结果输出路径 |

**示例请求**:

```json
{
  "action": "starthumaninloop",
  "data": {
    "scenarioPath": "C:/Projects/Scenarios/urban.json",
    "outputPath": "C:/Projects/Results/urban/",
    "participants": [
      {"id": "p1", "role": "commander"},
      {"id": "p2", "role": "operator"}
    ]
  }
}
```

**示例响应**:

```json
{
  "action": "starthumaninloop",
  "status": "success", 
  "taskId": "hil_20230505_123456",
  "scenarioPath": "C:/Projects/Scenarios/urban.json",
  "outputPath": "C:/Projects/Results/urban/"
}
```

#### 3.4 启动批量推演

**操作**: `startbatchinference`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| batchConfigPath | String | 批处理配置文件路径 | 是 |
| outputDir | String | 输出目录 | 是 |
| parallel | Boolean | 是否并行执行 | 否 |

**响应字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| taskId | String | 批处理任务标识符 |
| batchSize | Number | 批处理任务数量 |
| outputDir | String | 输出目录 |

**示例请求**:

```json
{
  "action": "startbatchinference",
  "data": {
    "batchConfigPath": "C:/Projects/Batch/config.json",
    "outputDir": "C:/Projects/Batch/results/",
    "parallel": true
  }
}
```

**示例响应**:

```json
{
  "action": "startbatchinference",
  "status": "success", 
  "taskId": "batch_20230505_123456",
  "batchSize": 10,
  "outputDir": "C:/Projects/Batch/results/"
}
```

### 4. 其他操作

#### 4.1 日志操作

**操作**: `log`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| level | String | 日志级别 (info, warning, error) | 是 |
| message | String | 日志消息 | 是 |
| source | String | 日志来源 | 否 |

**响应字段**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| timestamp | String | 日志记录时间戳 |
| logFile | String | 日志文件路径 |

**示例请求**:

```json
{
  "action": "log",
  "data": {
    "level": "warning",
    "message": "文件格式可能不兼容",
    "source": "terrain_editor"
  }
}
```

**示例响应**:

```json
{
  "action": "log",
  "status": "success", 
  "timestamp": "2023-05-05T12:34:56.789Z",
  "logFile": "C:/Logs/application.log"
}
```

### 常见错误状态码

| HTTP状态码 | 状态 | 描述 |
|------------|------|------|
| 400 | bad_request | 请求格式错误或参数不完整 |
| 404 | not_found | 请求的资源未找到 |
| 405 | error | 不支持的HTTP方法（仅支持POST） |
| 415 | error | 不支持的内容类型（仅支持application/json） |
| 500 | error | 服务器内部错误 |

## 注意事项

1. 所有请求必须使用POST方法，并且内容类型必须是`application/json`

### 5. 作战部署与规划操作

#### 5.1 作战部署与计划指令录入

**操作**: `battleDeploy`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String | 用户标识符 | 否 |
| scenarioId | Number | 想定ID | 是 |
| scenarioName | String | 想定名称 | 是 |
| filePath | String | XML文件路径 | 是 |

**响应字段**:

响应包含一个`metadata`对象，其中包含以下字段:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| scenarioId | Number | 想定ID |
| scenarioName | String | 想定名称 |
| filePath | String | XML文件路径 |

**示例请求**:

```json
{
  "action": "battleDeploy",
  "data": {
    "usrID": "user123",
    "scenarioId": 1001,
    "scenarioName": "城市攻防战役想定",
    "filePath": "C:/BattlePlans/urban_defense.xml"
  }
}
```

**示例响应**:

```json
{
  "action": "battleDeploy",
  "status": "success", 
  "metadata": {
    "scenarioId": 1001,
    "scenarioName": "城市攻防战役想定",
    "filePath": "C:/BattlePlans/urban_defense.xml"
  }
}
```

**注意**:
- 接口会验证XML文件路径是否存在
- scenarioName长度不应超过50个字符
- filePath长度不应超过255个字符

#### 5.2 引擎启动路径选择

**操作**: `chooseVBSPath`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String | 用户标识符 | 否 |

**响应字段**:

响应包含一个`metadata`对象，其中包含以下字段:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| vbsName | String | 引擎名称 |
| path | String | 启动绝对路径 |

**示例请求**:

```json
{
  "action": "chooseVBSPath",
  "data": {
    "usrID": "user123"
  }
}
```

**示例响应**:

```json
{
  "action": "chooseVBSPath",
  "status": "success", 
  "metadata": {
    "vbsName": "VBS4.0引擎",
    "path": "C:/Program Files/VBS4/bin/vbs.exe"
  }
}
```

**示例响应(用户取消选择):**

```json
{
  "action": "chooseVBSPath",
  "status": "canceled", 
  "metadata": {}
}
```

**注意**:
- 此操作会弹出系统原生的文件选择对话框
- 如果用户取消选择，将返回status为"canceled"的响应
- vbsName长度不应超过50个字符
- path长度不应超过200个字符

#### 5.3 同步配置文件

**操作**: `synchronizationConfig`

**请求参数**:

| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| usrID | String | 用户标识符 | 否 |

**响应字段**:

响应包含一个`metadata`对象，通常为空，仅表示操作的成功或失败状态。

**示例请求**:

```json
{
  "action": "synchronizationConfig",
  "data": {
    "usrID": "user123"
  }
}
```

**示例响应**:

```json
{
  "action": "synchronizationConfig",
  "status": "success", 
  "metadata": {}
}
```

**注意**:
- 此操作会触发C端同步配置文件并广播给所有客户端
- 操作成功时返回status为"success"，失败时返回status为"error"