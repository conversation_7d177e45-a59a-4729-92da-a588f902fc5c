<script lang="ts" setup>
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue';

import { useAntdDesignTokens } from '@vben/hooks';
import { preferences, usePreferences } from '@vben/preferences';

import { App, ConfigProvider, theme } from 'ant-design-vue';

import { antdLocale } from '#/locales';
import { antdvComponentStyle } from '#/preferences';

defineOptions({ name: 'App' });
const { isDark } = usePreferences();
const { tokens } = useAntdDesignTokens();
const DomRef = ref();
// 获取初始窗口大小--->这里可能存在一个问题
// 若用户ctrl+wheel已经进行缩放了，那么此时会基于这个已经缩放完的内容
// 进行调整
const width = window.innerWidth;
const height = window.innerHeight;

const autoResizeScreen = () => {
  const rect = document.querySelector('#app').getBoundingClientRect();
  const clientWidth = rect.width;
  const clientHeight = rect.height;
  const ratio = width / height;
  const scale =
    clientWidth / clientHeight > ratio
      ? height / clientHeight
      : width / clientWidth;

  const style = {
    transform: `scale(${scale})`,
    transformOrigin: 'top left',
  };
  Object.assign(DomRef.value.style, style);
};
const preventCtrilWheel = (e: WheelEvent) => {
  // 没ctrl，就直接放行
  if (!e.ctrlKey) {
    return;
  }
  if (e.deltaY < 0 && window.devicePixelRatio >= 1.5) {
    e.preventDefault();
  }
};
onMounted(() => {
  nextTick(() => {
    autoResizeScreen();
  });
  // window.addEventListener('resize', autoResizeScreen);
  window.addEventListener('wheel', preventCtrilWheel, {
    capture: false,
    passive: false,
  });
});
onUnmounted(() => {
  // window.removeEventListener('resize', autoResizeScreen);
  window.removeEventListener('wheel', preventCtrilWheel);
});
const tokenTheme = computed(() => {
  const algorithm = isDark.value
    ? [theme.darkAlgorithm]
    : [theme.defaultAlgorithm];

  // antd 紧凑模式算法
  if (preferences.app.compact) {
    algorithm.push(theme.compactAlgorithm);
  }
  // 组件细节
  return {
    algorithm,
    components: antdvComponentStyle,
    token: tokens,
    colorPrimary: '#008868',
  };
});
</script>

<template>
  <ConfigProvider :locale="antdLocale" :theme="tokenTheme">
    <App>
      <div ref="DomRef" class="size-full">
        <RouterView />
      </div>
    </App>
  </ConfigProvider>
</template>
