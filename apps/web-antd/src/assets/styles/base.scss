// ===== 📌 引入字体  ===== //
@font-face {
  font-family: 'HanScansBold';
  src: url('@/assets/fonts/Source_Han_Sans_SC_Bold.otf') format('opentype');
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'HanScansRegular';
  src: url('@/assets/fonts/Source_Han_Sans_SC_Regular.otf') format('opentype');
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'HanScansLightLight';
  src: url('@/assets/fonts/Source_Han_Sans_SC_Light_Light.otf') format('opentype');
  font-style: normal;
}

@font-face {
  font-family: 'HanScansMediumMedium';
  src: url('@/assets/fonts/Source_Han_Sans_SC_Medium_Medium.otf') format('opentype');
  font-style: normal;
}

@font-face {
  font-family: 'HanScansExtraLightExtraLight';
  src: url('@/assets/fonts/Source_Han_Sans_SC_ExtraLight_ExtraLight.otf') format('opentype');
  font-style: normal;
}

@font-face {
  font-family: 'HanScansHeavyHeavy';
  src: url('@/assets/fonts/Source_Han_Sans_SC_Heavy_Heavy.otf') format('opentype');
  font-style: normal;
}

@font-face {
  font-family: 'HanScansNormalNormal';
  src: url('@/assets/fonts/Source_Han_Sans_SC_Normal_Normal.otf') format('opentype');
  font-style: normal;
}

// ===== 设置根元素字体 ===== //
html,
body,
label {
  font-family: 'HanScansNormalNormal', sans-serif !important;
}

// ===== 所有元素及伪元素继承字体 ===== //
*,
*::before,
*::after {
  font-family: inherit !important;
}

// ===== 📦 引入变量文件 ===== //
@import './variables.scss';

// ===== 🔧 Checkbox 组件样式覆盖 ===== //
[data-state="checked"] {
  background-color: hsl(159, 64%, 35%) !important;
  color: $color-font-light !important;
  border-color: hsl(159, 64%, 35%) !important;
}

.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(159, 64%, 35%) !important;
  border-color: hsl(159, 64%, 35%) !important;
}

[data-state="checked"].bg-primary,
[data-state="checked"][class*="bg-primary"] {
  background-color: hsl(159, 64%, 35%) !important;
  border-color: hsl(159, 64%, 35%) !important;
}

[data-radix-collection-item][data-state="checked"] {
  background-color: hsl(159, 64%, 35%) !important;
  border-color: hsl(159, 64%, 35%) !important;
  color: $color-font-light !important;
}

.peer[data-state="checked"] {
  background-color: hsl(159, 64%, 35%) !important;
  border-color: hsl(159, 64%, 35%) !important;
}

.peer[data-state="checked"]:where(.h-4.w-4.shrink-0.rounded-sm.border) {
  background-color: hsl(159, 64%, 35%) !important;
  border-color: hsl(159, 64%, 35%) !important;
}

// ===== 🔲 按钮统一样式 ===== //
.button_customize_style {
  padding: 2px 14px !important;
  height: auto !important;
  border: $border-width-thick solid $color-border-transparent; // 使用变量替换 2px solid transparent
  background-color: $color-border-transparent; // 使用变量替换 transparent
  box-shadow: none;

  span {
    line-height: $line-height-button; // 使用变量替换 20px
    padding: 0 !important;
    display: inline-block;
    vertical-align: middle;
  }

  // 五种按钮样式（支持 hover / active / disabled）
  @for $i from 1 through 5 {
    &.button_#{$i} {
      $border-color: nth(($color-primary-1,
      $color-primary-2,
      $color-primary-3,
      $color-primary-4,
      $color-primary-5), $i);

      $hover-bg: nth(($color-primary-1-hover,
      $color-primary-2-hover,
      $color-primary-3-hover,
      $color-primary-4-hover,
      $color-primary-5-hover), $i);

      border-color: $border-color;

      span {
        color: $border-color;
      }

      &:hover,
      &:active,
      &.is-active,
      &.is-selected {
        background-color: $hover-bg;

        span {
          color: $color-font-light; // 使用变量替换 #fff
        }
      }

      &:disabled,
      &.is-disabled {
        opacity: $color-disabled-opacity; // 使用变量替换 0.5
        pointer-events: none;
        cursor: not-allowed;

        span {
          color: $color-font-disabled !important; // 使用变量替换 #ccc
        }
      }
    }
  }
}

.button_customize_style + .button_customize_style {
  margin-left: 10px;
}

// ===== 🔧 按钮 Mixin 定义 ===== //
@mixin custom-ant-btn($color, $hoverColor) {
  padding: 2px 14px !important;
  height: auto !important;
  border: $border-width-thick solid $color; // 使用变量替换 2px
  background-color: $color-border-transparent; // 使用变量替换 transparent
  box-shadow: none;

  span {
    line-height: $line-height-button; // 使用变量替换 20px
    padding: 0 !important;
    display: inline-block;
    color: $color;
  }

  &:hover,
  &:active,
  &.is-active,
  &.is-selected {
    background-color: $hoverColor;

    span {
      color: $color-font-light; // 使用变量替换 #fff
    }
  }

  &:disabled,
  &.is-disabled {
    opacity: $color-disabled-opacity; // 使用变量替换 0.5
    pointer-events: none;
    cursor: not-allowed;

    span {
      color: $color-font-disabled !important; // 使用变量替换 #ccc
    }
  }
}

@mixin custom-ant-btn-text($color, $hoverColor) {
  padding: 0 !important;
  height: auto !important;
  border: 0 solid $color-border-transparent; // 使用变量替换 transparent
  background-color: $color-border-transparent; // 使用变量替换 transparent
  box-shadow: none;

  span {
    line-height: $line-height-button; // 使用变量替换 20px
    padding: 0 !important;
    display: inline-block;
    color: $color;
  }

  &:hover,
  &:active,
  &.is-active,
  &.is-selected {
    background-color: $color-border-transparent !important;

    span {
      color: $hoverColor;
    }
  }

  &:disabled,
  &.is-disabled {
    opacity: $color-disabled-opacity; // 使用变量替换 0.5
    pointer-events: none;
    cursor: not-allowed;

    span {
      color: $color-font-disabled !important; // 使用变量替换 #ccc
    }
  }
}

// ===== 🔲 Ant Design 按钮样式 ===== //
.ant-btn {
  text-align: center;
  height: 100%;
  padding: 0;
}

.ant-btn-default {
  @include custom-ant-btn($color-primary-5, $color-primary-5-hover);
}

.ant-btn-icon-only {
  padding: 2px 0 !important;
  height: 100%;
}

.ant-btn-primary {
  @include custom-ant-btn($color-primary-1, $color-primary-1-hover);
}

.ant-btn-text {
  @include custom-ant-btn-text($color-primary-1, $color-primary-1-hover); // 修正：custom-ant-btn_text → custom-ant-btn-text
}

.ant-btn-default.ant-btn-dangerous {
  @include custom-ant-btn($color-primary-2, $color-primary-2-hover);
}

.ant-btn-primary.ant-btn-dangerous {
  @include custom-ant-btn($color-primary-2, $color-primary-2-hover);
}

.ant-btn-text.ant-btn-dangerous {
  @include custom-ant-btn_text($color-primary-2, $color-primary-2-hover); // 修正：custom-ant-btn_text → custom-ant-btn-text
}

.ant-btn-link {
  @include custom-ant-btn_text($color-primary-1, $color-primary-1-hover); // 修正：custom-ant-btn_text → custom-ant-btn-text
}

// ===== 📝 输入框样式 ===== //
.ant-input,
.ant-input-password,
.ant-input-number {
  background: $color-form-bg;
  border: $border-width-normal solid $color-primary-1; // 使用变量替换 1px
  color: $color-font-light;
  border-radius: $border-radius-medium; // 使用变量替换 5px
  line-height: $line-height-default; // 使用变量替换 1.6

  &:focus,
  &:hover,
  &:active {
    border-color: $color-primary-1-hover;
  }

  &::placeholder {
    color: $color-form-placeholder;
  }
}

.ant-input-number {
  border: $border-width-normal solid $color-form-placeholder; // 使用变量替换 1px
}

.ant-input-number-handler-wrap {
  background-color: $color-form-bg !important;
  border-inline-start: $border-width-normal solid $color-font-light; // 使用变量替换 1px

  .ant-input-number-handler {
    border-block-start: $border-width-normal solid $color-font-light; // 使用变量替换 1px
    border-start-end-radius: $border-radius-default; // 使用变量替换 0px

    .ant-input-number-handler-down-inner,
    .ant-input-number-handler-up-inner {
      color: $color-font-light;

      &:hover {
        color: $color-font-light;
      }
    }
  }
}

.ant-input-number .ant-input-number-handler:hover .ant-input-number-handler-up-inner,
.ant-input-number .ant-input-number-handler:hover .ant-input-number-handler-down-inner {
  color: var(--color-primary-1-hover) !important; // 使用变量替换 #fff
}

// ===== 📊 表格样式 ===== //
.ant-table {
  border: $border-width-normal solid $color-card-head-border !important;
  background: $color-gradient-center !important;
  border-radius: $border-radius-default !important; // 使用变量替换 0px

  &:hover {
    background: $color-gradient-center !important;
  }

  .ant-table-container {
    width: 100% !important; // 确保容器宽度不超出父元素
    overflow-x: auto !important; // 添加横向滚动条防止溢出

    .ant-table-thead {
      background-image: var(--bg-table-header) !important;
      background-size: cover !important;
      background-position: center !important;
      background-repeat: no-repeat !important;
      background-attachment: scroll !important;
      .ant-table-cell {
        background-color: $color-form-bg !important;
      }
    }

    .ant-table-header {
      border-radius: $border-radius-default !important; // 使用变量替换 0px

      table {
        border-radius: $border-radius-default; // 使用变量替换 0px
        width: 100% !important; // 限制内部表格宽度

        thead > tr {
          &:first-child > {
            *:first-child {
              border-start-start-radius: $border-radius-default; // 使用变量替换 0px
            }

            *:last-child {
              border-start-end-radius: $border-radius-default; // 使用变量替换 0px
            }
          }

          > th {
            background: $color-gradient-center;
          }
        }
      }
    }

    table {
      border-radius: $border-radius-default; // 使用变量替换 0px
      width: 100% !important; // 限制内部表格宽度

      thead > tr {

        &:first-child > {
          *:first-child {
            border-start-start-radius: $border-radius-default; // 使用变量替换 0px
          }

          *:last-child {
            border-start-end-radius: $border-radius-default; // 使用变量替换 0px
          }
        }

        > th {
          background: unset;
          font-size: 16px;
          padding: 8px 4px;
        }
      }
    }

    .ant-table-body {
      .ant-table-placeholder:hover {
        background: $color-gradient-center !important;
      }

      .ant-empty {
        .ant-empty-image {
          opacity: $color-disabled-opacity; // 使用变量替换 0.5
        }
      }
    }
  }

  .ant-table-body::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }

  .ant-table-body::-webkit-scrollbar-thumb {
    background-color: $color-scrollbar-thumb; // 使用变量替换 rgba(144, 147, 153, 0.3)
    border-radius: $border-radius-small; // 使用变量替换 4px
  }

  .ant-table-body::-webkit-scrollbar-track {
    background-color: $color-border-transparent; // 使用变量替换 transparent
  }
}

// ===== 📊 表格边框样式 ===== //
.ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr:last-child > td {
  border-bottom: $border-width-normal solid $color-card-head-border; // 使用变量替换 1px
}

.ant-table-wrapper .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before,
.ant-table-wrapper .ant-table-thead > tr > td:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
  background-color: $color-card-head-border !important;
}

.ant-table-wrapper .ant-table-tbody > tr.ant-table-placeholder:hover > td {
  background: $color-layout-border !important;
}

.ant-table-wrapper .ant-table-thead > tr > th,
.ant-table-wrapper .ant-table-thead > tr > td {
  border-bottom: $border-width-normal solid $color-card-head-border; // 使用变量替换 1px
}

.ant-table-wrapper .ant-table-thead th.ant-table-column-sort {
  background-color: $color-layout-bg !important;
}

.ant-table-wrapper .ant-table-thead th.ant-table-column-has-sorters:hover {
  background-color: $color-layout-bg !important;
}

.ant-table-wrapper .ant-table-cell-fix-left,
.ant-table-wrapper .ant-table-cell-fix-right {
  background: $color-layout-bg;
}

.ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr > td {
  border-top: $border-width-normal solid $color-card-head-border; // 使用变量替换 1px
}

.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td,
.ant-table-wrapper .ant-table-tbody > tr > td.ant-table-cell-row-hover {
  background-color: $color-card-head-border;
}

.ant-table-wrapper .ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: $color-card-head-border;
}

.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > th,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr > th,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > thead > tr > th,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > thead > tr > th,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > td,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > td,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tfoot > tr > th,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tfoot > tr > th,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tfoot > tr > th,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tfoot > tr > th,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tfoot > tr > td,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tfoot > tr > td,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tfoot > tr > td,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tfoot > tr > td {
  border-inline-end: $border-width-normal solid $color-card-head-border; // 使用变量替换 1px
  border-right: $border-width-normal solid $color-card-head-border; // 使用变量替换 1px
}

.ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td {
  border-bottom: $border-width-normal solid $color-card-head-border; // 使用变量替换 1px
}

.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table,
.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table {
  border-top: $border-width-normal solid $color-card-head-border; // 使用变量替换 1px
}

.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container {
  border-inline-start: $border-width-normal solid $color-card-head-border; // 使用变量替换 1px
}

.ant-table-wrapper .ant-table-cell-scrollbar:not([rowspan]) {
  box-shadow: 0 $border-width-normal 0 $border-width-normal $color-card-head-border; // 使用变量替换 1px
}

// ===== 🗃️ 模态框样式 ===== //
.ant-modal {
  .ant-modal-content {
    background-color: $color-layout-bg;
  }

  .ant-modal-header {
    background-color: $color-layout-bg;
  }
}

// ===== 抽屉样式 ===== //
.ant-drawer {
  .ant-drawer-content {
    background-color: $color-layout-bg;
  }

  .ant-drawer-header {
    background-color: $color-layout-bg;
  }
}

// ===== 🃏 卡片样式 ===== //
.ant-card {
  border: $border-width-normal solid $color-layout-border; // 使用变量替换 1px
  border-radius: $border-radius-default; // 使用变量替换 0
  background-image: var(--bg-card);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;

  .ant-card-head {
    background-color: $color-layout-bg;
    border-bottom: $border-width-normal solid $color-layout-border; // 使用变量替换 1px
    border-radius: $border-radius-default; // 使用变量替换 0
    margin-bottom: $border-width-thin; // 使用变量替换 0.5px
  }

  .ant-card-body {
    border-radius: $border-radius-default; // 使用变量替换 0
    height: 100%;

    &::-webkit-scrollbar-thumb {
      background-color: $color-primary-5 !important;
      border-radius: $border-radius-small !important; // 使用变量替换 4px
    }

    &::-webkit-scrollbar-track {
      background-color: $color-border-transparent !important;
    }
  }
}

// ===== 🔽 选择框样式 ===== //
.ant-select-selector {
  background: $color-form-bg;
  background-color: $color-form-bg !important;
  border-radius: $border-radius-default; // 使用变量替换 0
  border: none !important;
  color: $color-font-light;
  line-height: $line-height-default; // 使用变量替换 1.6
}

.ant-select,
.ant-input-search,
.ant-input-affix-wrapper {
  border: $border-width-normal solid $color-primary-1;
  border-radius: $border-radius-medium; // 使用变量替换 5px
  background: $color-border-transparent;

  &:hover,
  &:focus {
    border: $border-width-normal solid $color-primary-1-hover;
  }

  .ant-select-selector {
  }

  .ant-input-search-button {
    color: $color-font-light !important;
    border: $border-width-normal solid $color-form-placeholder; // 使用变量替换 1px
    padding-top: 0;
    padding-bottom: 0;
    border-start-start-radius: 0;
    border-start-end-radius: $border-radius-medium !important;
    border-end-end-radius: $border-radius-medium !important;
    border-end-start-radius: 0;
    height: $input-height !important;

    &:hover,
    &:active,
    &.is-active,
    &.is-selected {
      color: $color-primary-1;

      span {
        color: $color-primary-1;
      }
    }

    &:disabled,
    &.is-disabled {
      opacity: $color-disabled-opacity; // 使用变量替换 0.5
      pointer-events: none;
      cursor: not-allowed;

      span {
        color: $color-font-disabled !important; // 使用变量替换 #ccc
      }
    }
  }
}

// ===== 🌳 树形选择框样式 ===== //
.ant-select-tree-list {
  .ant-select-tree-list-holder-inner {
    background: $color-form-bg;

    .ant-select-tree-node-selected {
      background-color: $color-primary-5-hover !important;
    }
  }
}

// ===== 🌲 树结构样式 ===== //
.ant-tree {
  background: $color-border-transparent; // 使用变量替换 transparent

  &.ant-tree_transparent {
    background-color: $color-border-transparent; // 使用变量替换 transparent
  }

  .ant-tree-treenode-selected {
    .ant-tree-node-selected {
      background-color: $color-border-transparent !important;
      color: $color-tree-selected;
    }
  }
}

.ant-tree-checkbox {
  .ant-tree-checkbox-inner {
    background-color: $color-border-transparent !important;
    border-color: $color-primary !important;
  }
}

// ===== ☑️ 复选框样式 ===== //
.ant-checkbox-wrapper {
  .ant-checkbox-inner {
    background: $color-layout-bg;
    border: $border-width-thin solid $color-font-light; // 使用变量替换 0.5px
  }

  &:hover .ant-checkbox-inner {
    border-color: $color-tree-selected !important;
  }
}

.ant-checkbox-wrapper-checked {
  .ant-checkbox-checked {
    .ant-checkbox-inner {
      background-color: $color-tree-selected !important;
      border: $border-width-thin solid $color-border-transparent !important; // 使用变量替换 0.5px
    }
  }
}

// ===== 📻 单选框样式 ===== //
.ant-radio-group {
  .ant-radio-inner {
    background-color: $color-border-transparent; // 使用变量替换 transparent
    border-color: $color-tree-selected;
  }

  .ant-radio-wrapper .ant-radio-checked .ant-radio-inner {
    background-color: $color-tree-selected;
  }
}

// ===== 📋 标签页样式 ===== //
.ant-tabs {
  .ant-tabs-tab-btn {
    color: $color-font-light;

    &:focus {
      color: $color-primary-1 !important;
    }
  }

  .ant-tabs-tab-active {
    .ant-tabs-tab-btn {
      color: $color-primary-1 !important;
    }
  }

  .ant-tabs-ink-bar {
    background-color: $color-primary-1;
  }

  &.ant-tabs-card {
    .ant-tabs-tab {
      background-color: $color-form-bg;
    }

    .ant-tabs-tab-active {
      border-bottom-color: $color-primary-1 !important;
    }
  }
}

// ===== 📄 分页样式 ===== //
.ant-pagination {
  .ant-pagination-item-active {
    border-color: $color-primary-1;
    background-color: unset;
    color: $color-font-light;

    a {
      color: $color-font-light;
    }
  }
}

// ===== 📜 滚动条全局样式 ===== //
::-webkit-scrollbar-thumb {
  background-color: $color-primary-5 !important;
  border-radius: $border-radius-small !important; // 使用变量替换 4px
}

::-webkit-scrollbar-track {
  background-color: $color-border-transparent !important;
}

// ===== 📅 日期选择器样式 ===== //
.ant-picker {
  border: $border-width-normal solid $color-border-transparent; // 使用变量替换 1px, transparent
  border-radius: $border-radius-default; // 使用变量替换 0px
  background-color: $color-layout-border;

  .ant-picker-input {
    input::placeholder {
      color: $color-form-placeholder;
    }
  }

  &-input {
    color: $color-font-light;
  }

  .ant-picker-clear {
    color: $color-font-light;
    background: $color-layout-border;
  }
}

.ant-picker-panels {
  background: $color-layout-border;

  .ant-picker-cell-in-view.ant-picker-cell-in-range::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before,
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {
    background: $color-gradient-center;
  }
}

// ===== 🏷️ 标签样式 ===== //
.ant-tag-success,
.ant-tag-processing {
  color: $color-primary-1;
  background: $color-tag-bg; // 使用变量替换 hsla(206, 100%, 95%, 0.274)
  border-color: $color-primary-1;
}

// ===== 📐 页面内容高度设置 ===== //
.page_content_customize_style {
  height: calc(100vh - 100px) !important;
  margin-top: 0px;
  padding-top: 0px !important;
  padding-left: 14px !important;
  padding-bottom: 0px !important;
}

// ===== 🎨 Layout 无背景样式 ===== //
.layout_customize_style {
  background: none;

  .ant-layout-sider {
    flex: 0 0 257px !important;
    max-width: 257px !important;
    width: 257px !important;
    background: none;
  }
}

// ===== 🎨 Layout 带背景样式 ===== //
.layout_customize_color_style {
  background: var(--color-body-bg);
  border: $border-width-normal $color-layout-border solid; // 使用变量替换 1px

  .ant-layout-sider {
    background: none;
  }
}

// ===== 📱 侧边栏样式 ===== //
.side_customize_style {
  width: 257px !important;
  background: linear-gradient(270deg, $color-overlay-light, $color-overlay-lighter),
  linear-gradient(323deg,
      $color-gradient-left 0%,
      $color-gradient-center 40%,
      $color-gradient-center 45%,
      $color-gradient-center 50%,
      $color-gradient-center 55%,
      $color-card-head-border 100%);
  border: none;
  border-radius: $border-radius-default;
  position: relative;

  .ant-card-head-wrapper {
    position: relative;
    margin-top: 4px;
  }

  .ant-card-head {
    position: relative;
    border-radius: $border-radius-default;
    height: 48px;
    background: linear-gradient(270deg,
      $color-gradient-left 0%,
      $color-gradient-center 40%,
      $color-gradient-center 45%,
      $color-gradient-center 50%,
      $color-gradient-center 55%,
      $color-card-head-border 100%);
    border-bottom: $border-width-normal $color-card-head-border solid;
    box-shadow: 0 3px 11px $color-shadow-dark;
    z-index: 2;
  }

  .ant-card-body {
    height: calc(100% - 148px);
    z-index: 1;
    padding-left: 2px;
    padding-top: 10px;
    border-radius: $border-radius-default;
    overflow-y: auto;
    background: linear-gradient(270deg, $color-overlay-light, $color-overlay-lighter),
    linear-gradient(323deg,
        $color-gradient-left 0%,
        $color-gradient-center 40%,
        $color-gradient-center 45%,
        $color-gradient-center 50%,
        $color-gradient-center 80%,
        $color-card-head-border 100%);
    border: none;

    &::-webkit-scrollbar-thumb {
      background-color: $color-primary-5 !important;
      border-radius: $border-radius-small !important;
    }

    &::-webkit-scrollbar-track {
      background-color: $color-border-transparent !important;
    }
  }

  // 树形样式
  .tree_customize_style {
    height: calc(100vh - 348px) !important;
    background-color: $color-border-transparent;
  }
}

.side_customize_body_size {
  .ant-card-body {
    height: calc(100vh - 177px);
  }
}


// ===== 📐 全局右侧内样式 ===== //
.normal_menu_ul {
  li {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    align-content: center !important;

    &.is-active {
      border-bottom: 2px solid;
      border-image-source: linear-gradient(90deg,
        var(--sidebar-gradient-start-end) 0%,
        var(--sidebar-gradient-center) 50%,
        var(--sidebar-gradient-start-end) 100%);
      border-image-slice: 3; // 必需属性
      border-image-width: 2; // 可选
      border-image-repeat: stretch; // 可选
      background: linear-gradient(72deg, var(--color-overlay-light), var(--color-overlay-lighter)),
      linear-gradient(184deg, rgba(12, 135, 100, 0.874) 0%, rgba(12, 135, 100, 0.541) 40% rgba(12, 135, 101, 1) 45%,
          rgba(12, 135, 101, 1) 50%, rgba(12, 135, 101, 1) 55% rgba(12, 135, 100, 0.422) 100%) !important;
    }


    svg {
      font-size: 25px !important;
    }

    div {
      width: auto !important;
      font-size: 24px !important;
      margin-top: 0 !important;
      margin-left: 10px;
    }
  }
}

.scrollbar_top_shadow {
  background: linear-gradient(to bottom, var(--color-form-bg), transparent);
}

.scrollbar_bottom_shadow {
  background: linear-gradient(to top, var(--color-form-bg), transparent);
}

.scroll_bar_border_border {
  border-color: var(--color-form-bg) !important;
}

.bg-sidebar-deep {
  background: linear-gradient(270deg, $color-overlay-light, $color-overlay-lighter),
  linear-gradient(323deg,
      $color-gradient-left 0%,
      $color-gradient-center 40%,
      $color-gradient-center 45%,
      $color-gradient-center 50%,
      $color-gradient-center 55%,
      $color-card-head-border 100%) !important;
}

// ===== 📐 右侧内容间距 ===== //
.layout_content_customize_style {
  margin-top: 20px;
  padding-left: 14px !important;
}

// ===== 🃏 主卡片样式 ===== //
.main_card_customize_style {
  border-radius: $border-radius-default; // 使用变量替换 0
  background-color: var(--color-body-bg);
  border: none;

  .ant-card-head {
    border-bottom: $border-width-thick $color-layout-border solid; // 使用变量替换 2px
  }

  .ant-card-body {
    overflow-y: auto;
  }
}

.main_card_customize_style::-webkit-scrollbar-thumb {
  background-color: $color-primary-5 !important;
  border-radius: $border-radius-small !important; // 使用变量替换 4px
}

.main_card_customize_style::-webkit-scrollbar-track {
  background-color: $color-border-transparent !important;
}

.main_card_customize_style_266 {
  overflow-y: auto;
  height: calc(100vh - 266px) !important;
}

.main_card_customize_style_body_266 {
  .ant-card-body {
    overflow-y: auto;
    height: calc(100vh - 320px) !important;
  }
}

// ===== 📝 表单样式 ===== //
.ant-form {
  &.ant-form-vertical {
    .ant-form-item-label {
      padding-bottom: 8px;
    }
  }
}

// ===== 💻 CodeMirror 样式 ===== //
.codemirror_customize_style {
  .cm-gutters {
    background: $color-form-bg;
    color: $color-font-light;

    .cm-activeLineGutter {
      background-color: $color-form-bg;
      color: $color-font-light;
    }
  }

  .cm-content {
    background-color: $color-form-bg !important;
  }
}

// ===== 🔽 下拉选择器样式 ===== //
.ant-select-dropdown {
  background: $color-form-bg;

  .ant-select-item-option-selected {
    background-color: $color-primary-5-hover !important;
  }
}

// ===== 📊 按钮表格样式 ===== //
.button_table_customize_style {
  padding: 6px 0;
  border: solid $border-width-normal $color-form-bg; // 使用变量替换 1px

  &-button {
    margin-left: 8px;
    margin-bottom: 6px;
  }
}

.page_title {
  background-color: $color-form-bg !important;
}

// ===== 🧭 菜单导航样式 ===== //
.vben-sub-menu {
  div {
    .vben-sub-menu-content {
      background: $color-menu-bg !important; // 使用变量替换 #064e3a
    }

    &.is-active,
    &:hover {
      .is-active {
        background: $color-menu-active !important; // 使用变量替换 #1bd795
      }
    }
  }
}

.bg-popover {
  background: $color-menu-bg !important; // 使用变量替换 #064e3a
  border: none !important;

  .vben-menu__popup {
    background: $color-menu-bg !important; // 使用变量替换 #064e3a
    border: none !important;

    .vben-menu {
      background: $color-menu-bg !important; // 使用变量替换 #064e3a
      padding-bottom: 10px;

      .vben-menu-item__content {
        justify-content: center;
      }
    }
  }
}

// ===== 🃏 item卡片样式 ===== //
.item-card {
  background-image: var(--bg-item-card); // 使用变量替换背景图片路径
  border: none;
  border-radius: $border-radius-default; // 使用变量替换 0
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .ant-card-body {
    background-color: $color-border-transparent !important; // 使用变量替换 transparent
  }
}
