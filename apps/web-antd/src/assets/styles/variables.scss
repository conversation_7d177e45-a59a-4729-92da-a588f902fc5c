// ===== 🎨 CSS 变量定义 ===== //
:root {
  // ===== 头部相关颜色 ===== //
  --header-text-color: hsl(159, 78%, 47%);

  // ===== 主要颜色系统 ===== //
  --color-primary: hsl(159, 77%, 48%);
  --color-primary-1: hsl(158, 100%, 84%);
  --color-primary-1-hover: hsl(159, 77%, 48%);
  --color-primary-2: #acffe1;
  --color-primary-2-hover: #1cd996;
  --color-primary-3: #acffe1;
  --color-primary-3-hover: #1cd996;
  --color-primary-4: #acffe1;
  --color-primary-4-hover: #1cd996;
  --color-primary-5: #acffe1;
  --color-primary-5-hover: #1cd996;

  // ===== 布局相关颜色 ===== //
  --color-layout-bg: #015845;
  --color-layout-border: #0d7745;
  --color-card-head-border: #0d7745;
  --color-body-bg: hsla(167, 98%, 17%, 0.2);

  // ===== 表单相关颜色 ===== //
  --color-form-bg: #015845;
  --color-form-placeholder: #acffe1;

  // ===== 字体颜色 ===== //
  --color-font-light: #fff;
  --color-font-disabled: #ccc;
  --color-font-dark: #000;
  --color-tree-selected: #1cd996;

  // ===== 渐变相关颜色 ===== //
  --color-gradient-left: #04674e;
  --color-gradient-center: #025c47;

  // ===== 透明度相关颜色 ===== //
  --color-overlay-light: rgba(255, 255, 255, 0.08);
  --color-overlay-lighter: rgba(255, 255, 255, 0.01);
  --color-shadow-dark: rgba(0, 0, 0, 0.15);
  --color-scrollbar-thumb: rgba(144, 147, 153, 0.3);
  --color-tag-bg: hsla(206, 100%, 95%, 0.274);

  // ===== 菜单相关颜色 ===== //
  --color-menu-bg: #064e3a;
  --color-menu-active: #1bd795;
  --color-menu-text: var(--color-font-light);

  // ===== 禁用状态颜色 ===== //
  --color-disabled-opacity: 0.5;
  --color-border-transparent: transparent;

  // ===== Checkbox/Primary 相关 ===== //
  --primary: 159, 64%, 35%;
  --primary-foreground: 210, 40%, 98%;
  --primary-50: 0, 100%, 97%;
  --primary-100: 0, 100%, 95%;
  --primary-200: 0, 96%, 89%;
  --primary-300: 0, 94%, 82%;
  --primary-400: 0, 91%, 73%;
  --primary-500: 0, 100%, 50%;
  --primary-600: 0, 85%, 47%;
  --primary-700: 0, 87%, 42%;

  // ===== 数值相关 ===== //
  --border-radius-default: 0px;
  --border-radius-small: 4px;
  --border-radius-medium: 5px;
  --border-width-thin: 0.5px;
  --border-width-normal: 1px;
  --border-width-thick: 2px;
  --line-height-default: 1.6;
  --line-height-button: 20px;
  // ===== 侧边导航栏渐变样式 ===== //
  --sidebar-gradient-start-end: rgba(55, 153, 153, 0);
  --sidebar-gradient-center: hsla(159, 77%, 48%, 0.5);


  // ===== 背景图片路径 ===== //
  --bg-item-card: url('/main/item-card-bg.png');
  --bg-card: url('/bgImg/card-bg.png');
  --bg-table-header: url('/bgImg/table-header-bg.png');

  --input-height: 32px;
}


// ===== 🌓 主题覆盖 ===== //
.dark {
  --primary: 0 100% 50%;
  --primary-foreground: 210 40% 8%;
  --menu-item-active-color: hsl(var(--primary));
  --menu-item-active-background-color: hsl(var(--primary) / 15%);
}

.light {
  --menu-item-active-color: hsl(var(--primary));
  --menu-item-active-background-color: hsl(var(--primary) / 15%);
}

// ===== 🎨 SCSS 变量映射 ===== //
$color-primary: var(--color-primary);
$color-primary-1: var(--color-primary-1);
$color-primary-1-hover: var(--color-primary-1-hover);
$color-primary-2: var(--color-primary-2);
$color-primary-2-hover: var(--color-primary-2-hover);
$color-primary-3: var(--color-primary-3);
$color-primary-3-hover: var(--color-primary-3-hover);
$color-primary-4: var(--color-primary-4);
$color-primary-4-hover: var(--color-primary-4-hover);
$color-primary-5: var(--color-primary-5);
$color-primary-5-hover: var(--color-primary-5-hover);

$color-layout-bg: var(--color-layout-bg);
$color-layout-border: var(--color-layout-border);
$color-card-head-border: var(--color-card-head-border);

$color-form-bg: var(--color-form-bg);
$color-form-placeholder: var(--color-form-placeholder);

$color-font-light: var(--color-font-light);
$color-font-disabled: var(--color-font-disabled);
$color-font-dark: var(--color-font-dark);
$color-tree-selected: var(--color-tree-selected);

$color-gradient-left: var(--color-gradient-left);
$color-gradient-center: var(--color-gradient-center);

$color-overlay-light: var(--color-overlay-light);
$color-overlay-lighter: var(--color-overlay-lighter);
$color-shadow-dark: var(--color-shadow-dark);
$color-scrollbar-thumb: var(--color-scrollbar-thumb);
$color-tag-bg: var(--color-tag-bg);

$color-menu-bg: var(--color-menu-bg);
$color-menu-active: var(--color-menu-active);
$color-menu-text: var(--color-menu-text);

$color-disabled-opacity: var(--color-disabled-opacity);
$color-border-transparent: var(--color-border-transparent);

$border-radius-default: var(--border-radius-default);
$border-radius-small: var(--border-radius-small);
$border-radius-medium: var(--border-radius-medium);
$border-width-thin: var(--border-width-thin);
$border-width-normal: var(--border-width-normal);
$border-width-thick: var(--border-width-thick);
$line-height-default: var(--line-height-default);
$line-height-button: var(--line-height-button);

$input-height: var(--input-height);
