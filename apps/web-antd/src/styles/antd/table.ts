import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context';

const table: ThemeConfig['components'] = {
  Table: {
    borderRadius: 0, // 表格圆角
    // colorBgContainer: '#014A7F', // 表格背景色
    // colorFillAlter: '#0065AB', // 表格条纹行颜色
    // colorFillContentHover: '#0065AB', // 鼠标悬浮颜色
    // colorBorderSecondary: '#0065AB', // 边框颜色
    paddingContentVerticalLG: 0, // 垂直内边距
    fontFamily: 'Source Han Sans SC Bold', // 字体
    fontWeightStrong: 700, // 加粗
  },
};

export default table;
