<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message, Modal } from 'ant-design-vue';

import {
  deleteEvaluateTask,
  getEvaluateTaskInfo,
  pageEvaluateTask,
} from '#/api/module4';
import AddTaskModal from '#/views/exp-analyze/add-task-modal.vue';
import TaskDispath from '#/views/exp-analyze/task-dispath.vue';

// 任务列表数据
const taskList = ref([]);
const loading = ref(false);
const loadingMore = ref(false);
const hasMore = ref(true);

// 右侧表格loading状态
const tableLoading = ref(false);

// 分页参数
const pagination = ref({
  pageNumber: 1,
  pageSize: 20,
  total: 0,
});

// 加载任务列表
const loadTaskList = async (isLoadMore = false) => {
  try {
    if (isLoadMore) {
      loadingMore.value = true;
    } else {
      loading.value = true;
      pagination.value.pageNumber = 1;
      taskList.value = [];
    }

    const response = await pageEvaluateTask({
      pageNumber: pagination.value.pageNumber,
      pageSize: pagination.value.pageSize,
    });

    if (response) {
      const newTasks = response.records || [];

      if (isLoadMore) {
        taskList.value.push(...newTasks);
      } else {
        taskList.value = newTasks;
      }

      pagination.value.total = response.total || 0;
      pagination.value.pageNumber =
        response.current || pagination.value.pageNumber;

      // 判断是否还有更多数据
      hasMore.value = taskList.value.length < pagination.value.total;

      // 如果是首次加载且有数据，默认选中第一项
      if (!isLoadMore && newTasks.length > 0 && !selectedTask.value) {
        await handleTaskSelect(newTasks[0]);
      }
    }
  } catch (error) {
    console.error('加载任务列表失败:', error);
    message.error('加载任务列表失败');
  } finally {
    loading.value = false;
    loadingMore.value = false;
  }
};

// 加载更多
const handleLoadMore = () => {
  if (hasMore.value && !loadingMore.value) {
    pagination.value.pageNumber += 1;
    loadTaskList(true);
  }
};

// 刷新列表
const refreshTaskList = () => {
  selectedTask.value = null; // 清空当前选中的任务
  loadTaskList(false);
};

// 获取表格数据源
const getTableDataSource = (task) => {
  if (!task || !task.evaluateTaskSamples) return [];

  // 从evaluateTaskSamples中的每个样本的deduceRecordVOS中提取数据
  const records = [];
  task.evaluateTaskSamples.forEach((sample) => {
    if (sample.deduceRecordVOS && Array.isArray(sample.deduceRecordVOS)) {
      sample.deduceRecordVOS.forEach((record) => {
        records.push({
          ...record,
          sampleName: sample.sampleName,
          scenarioName: task.scenarioName,
          schemeName: task.schemeName,
          deduceRecord: record, // 兼容原有结构
        });
      });
    }
  });
  return records;
};

// 选择任务
const handleTaskSelect = async (task) => {
  if (!task) return;

  try {
    // 设置右侧表格loading状态
    tableLoading.value = true;

    // 获取任务详情
    selectedTask.value = await getEvaluateTaskInfo(task.id);
  } catch (error) {
    console.error('获取任务详情失败:', error);
    message.error('获取任务详情失败');
    // 如果获取详情失败，至少设置基本信息
    selectedTask.value = task;
  } finally {
    // 无论成功还是失败都要关闭loading
    tableLoading.value = false;
  }
};

const addTaskVisible = ref(false);
const taskDispathVisible = ref(false);
const addTaskModalRef = ref();

// 选中的任务
const selectedTask = ref(null);

// 编辑任务相关
const editTaskVisible = ref(false);
const editTaskData = ref(null);

// 表格列定义
const tableColumns = [
  {
    title: '想定名称',
    dataIndex: 'scenarioName',
    width: 120,
  },
  {
    title: '方案名称',
    dataIndex: 'schemeName',
    width: 120,
  },
  {
    title: '样本名称',
    dataIndex: 'sampleName',
    width: 100,
  },
  {
    title: '训练模式',
    dataIndex: ['deduceRecord', 'trainMode'],
    width: 120,
    customRender: ({ text }) => {
      const modeMap = { 1: '单装实验', 2: '作战单元/战术行动实验' };
      return modeMap[text] || text;
    },
  },
  {
    title: '训练类型',
    dataIndex: ['deduceRecord', 'trainType'],
    width: 120,
    customRender: ({ text }) => {
      const typeMap = { 1: 'AI自动推演', 2: '人在环', 3: '批量推演' };
      return typeMap[text] || text;
    },
  },
  {
    title: '数据采集时间',
    dataIndex: ['deduceRecord', 'timeStamp'],
    width: 160,
    customRender: ({ text }) => {
      if (!text) return '-';
      // 将时间戳转换为日期格式
      const date = new Date(Number(text));
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    },
  },
  {
    title: '创建时间',
    dataIndex: ['deduceRecord', 'createTime'],
    width: 160,
  },
];

const openTaskDispathModal = () => {
  taskDispathVisible.value = true;
};

// 处理任务下发成功
const handleTaskDispatchSuccess = () => {
  taskDispathVisible.value = false;
};

// 处理任务下发取消
const handleTaskDispatchCancel = () => {
  taskDispathVisible.value = false;
};

const openAddTask = () => {
  addTaskVisible.value = true;
};

// 处理新增任务成功
const handleAddTaskSuccess = async () => {
  addTaskVisible.value = false;
  message.success('任务创建成功');
  // 刷新任务列表，会自动选中第一项
  selectedTask.value = null;
  await loadTaskList(false);
};

// 处理新增任务取消
const handleAddTaskCancel = () => {
  addTaskVisible.value = false;
};

// 编辑任务
const handleEditTask = async (task) => {
  if (!task) return;

  try {
    // 设置右侧表格loading状态
    tableLoading.value = true;

    // 获取任务详情
    const taskDetail = await getEvaluateTaskInfo(task.id);
    // 设置选中的任务为完整的详情数据，确保右侧表格有数据
    selectedTask.value = taskDetail;
    editTaskData.value = taskDetail;
    editTaskVisible.value = true;
  } catch (error) {
    console.error('获取任务详情失败:', error);
    message.error('获取任务详情失败');
    // 如果获取详情失败，至少设置基本信息
    selectedTask.value = task;
  } finally {
    // 无论成功还是失败都要关闭loading
    tableLoading.value = false;
  }
};

// 删除任务
const handleDeleteTask = (task) => {
  if (!task) return;

  // 先选中当前任务
  selectedTask.value = task;

  // 显示删除确认对话框
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除任务"${task.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await deleteEvaluateTask(task.id);
        message.success('删除成功');

        // 重新加载任务列表，会自动选中第一项
        selectedTask.value = null;
        await loadTaskList(false);
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理编辑任务成功
const handleEditTaskSuccess = async () => {
  editTaskVisible.value = false;
  editTaskData.value = null;
  message.success('任务编辑成功');
  // 刷新任务列表，会自动选中第一项
  selectedTask.value = null;
  await loadTaskList(false);
};

// 处理编辑任务取消
const handleEditTaskCancel = () => {
  editTaskVisible.value = false;
  editTaskData.value = null;
};

// 组件挂载时加载数据
onMounted(() => {
  loadTaskList();
});
</script>

<template>
  <Page auto-content-height>
    <div class="flex min-h-full w-[20%] flex-col rounded border shadow">
      <!-- 标题栏 -->
      <div
        class="flex items-center justify-between rounded-t border-b px-3 py-[10px] text-left font-semibold"
      >
        <span>任务列表</span>
        <div class="flex gap-1">
          <a-button type="primary" size="small" @click="openTaskDispathModal">
            任务下发
          </a-button>
          <a-button size="small" @click="openAddTask"> 新增任务</a-button>
        </div>
      </div>

      <div class="flex-1 overflow-y-auto px-2 py-1" v-loading="loading">
        <div
          v-if="taskList.length === 0 && !loading"
          class="p-3 text-center text-sm text-gray-500"
        >
          <div class="mb-2">暂无任务数据</div>
          <a-button size="small" @click="openAddTask">
            创建第一个任务
          </a-button>
        </div>

        <div v-else>
          <div
            v-for="item in taskList"
            :key="item.id"
            class="mx-0 cursor-pointer rounded border border-transparent px-3 py-2 text-sm transition-colors duration-200 ease-in-out hover:border-green-400 hover:bg-green-400 hover:text-green-400"
            :class="{
              'bg-green-500 font-medium text-green-800':
                selectedTask?.id === item.id,
            }"
            @click="handleTaskSelect(item)"
          >
            <div class="flex w-full items-start justify-between">
              <div class="min-w-0 flex-1">
                <div
                  class="mb-1 truncate text-sm font-medium text-[var(--color-primary-1)]"
                >
                  {{ item.name }}
                </div>
                <div class="mb-1 text-xs text-[var(--color-primary)]">
                  类型: {{ item.type === 1 ? '单装实验' : '作战单元实验' }}
                </div>
                <div
                  class="truncate text-xs text-[var(--color-primary)]"
                  v-if="item.evaluateSchemeName"
                >
                  评估方案: {{ item.evaluateSchemeName }}
                </div>
              </div>
              <div class="ml-2 flex flex-shrink-0 gap-1">
                <a-button
                  size="small"
                  danger
                  type="text"
                  @click.stop="handleEditTask(item)"
                  class="!h-auto !px-2 !py-1 text-xs !text-[hsl(var(--primary))] hover:text-blue-800"
                >
                  编辑
                </a-button>
                <a-button
                  size="small"
                  type="text"
                  danger
                  @click.stop="handleDeleteTask(item)"
                  class="!h-auto !px-2 !py-1 text-xs"
                >
                  删除
                </a-button>
              </div>
            </div>
          </div>

          <!-- 加载更多按钮 -->
          <div v-if="hasMore" class="mt-4 px-3 text-center">
            <a-button
              :loading="loadingMore"
              @click="handleLoadMore"
              size="small"
              block
            >
              {{ loadingMore ? '加载中...' : '加载更多' }}
            </a-button>
          </div>
          <div
            v-else-if="taskList.length > 0"
            class="mt-4 px-3 text-center text-xs text-gray-500"
          >
            已加载全部数据
          </div>
        </div>
      </div>
    </div>

    <div class="flex-1">
      <a-table
        :columns="tableColumns"
        :data-source="getTableDataSource(selectedTask)"
        :loading="tableLoading"
        :pagination="false"
        bordered
        row-key="id"
        size="small"
      >
        <template #empty>
          <div class="py-8 text-center text-gray-500">
            {{ selectedTask ? '该任务暂无推演记录' : '请选择左侧任务查看详情' }}
          </div>
        </template>
      </a-table>
    </div>

    <a-modal
      width="70%"
      v-model:open="addTaskVisible"
      destroy-on-close
      :footer="null"
      :closable="false"
      title="新增评估任务"
    >
      <AddTaskModal
        ref="addTaskModalRef"
        @success="handleAddTaskSuccess"
        @cancel="handleAddTaskCancel"
      />
    </a-modal>

    <!-- 编辑任务模态框 -->
    <a-modal
      width="70%"
      v-model:open="editTaskVisible"
      destroy-on-close
      :footer="null"
      :closable="false"
      title="编辑评估任务"
    >
      <AddTaskModal
        :edit-data="editTaskData"
        @success="handleEditTaskSuccess"
        @cancel="handleEditTaskCancel"
      />
    </a-modal>
    <a-modal
      v-model:open="taskDispathVisible"
      title="任务下发"
      :footer="null"
      :closable="false"
      destroy-on-close
      width="500px"
    >
      <TaskDispath
        :selected-task="selectedTask"
        @success="handleTaskDispatchSuccess"
        @cancel="handleTaskDispatchCancel"
      />
    </a-modal>
  </Page>
</template>
