<script setup lang="ts">
import type { Ref } from 'vue';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { componentAddressDiagnosis } from '#/store/componentAddressDiagnosis';
import LeftCollapse from '#/views/exp-analyze/eval-plan-component/left-collapse.vue';
import MiddleTree from '#/views/exp-analyze/eval-plan-component/middle-tree.vue';

export interface ItreeData {
  id?: number | string;
  evaluateSchemeName: string;
  description: string;
  evaluateType: number;
}
const middleTreeRef = ref<any>();
const activateKey = ref('1');
const chooseTemplate: Ref<ItreeData> = ref(null);
const getActivateKey = (key: string) => {
  activateKey.value = key;
};
const getChooseTemplate = (template: ItreeData) => {
  chooseTemplate.value = template;
  const evaluateSchemeIdStore = componentAddressDiagnosis();
  if (template.id) {
    evaluateSchemeIdStore.setEvaluateSchemeId(template.id);
  }

  const activeTabKey = middleTreeRef.value.indicatorManagerRef.activeTabKey;
  if (activeTabKey === 'list') {
    middleTreeRef.value.indicatorManagerRef.indicatorListRef.indicatorTypesChange(
      template.id,
    );
  } else if (activeTabKey === 'weight') {
    middleTreeRef.value.indicatorManagerRef.indicatorWeightRef.indicatorTypesChange(
      template.id, 
    );
  } else {
    middleTreeRef.value.indicatorManagerRef.indicatorAlgorithmRef.indicatorTypesChange(
      template.id,
    );
  }
};
</script>

<template>
  <Page content-class="flex gap-4" class="eval-plan-design">
    <a-layout class="eval-plan-desig-layout-left gap-2">
      <a-layout-sider width="20%">
        <LeftCollapse @get-template="getChooseTemplate" />
      </a-layout-sider>
      <a-layout-content class="middle-tree-div">
        <MiddleTree
          :template="chooseTemplate"
          @get-choose-key="getActivateKey"
          ref="middleTreeRef"
        />
      </a-layout-content>
    </a-layout>
  </Page>
</template>

<style lang="scss" scoped>
.eval-plan-design {
  ::v-deep .p-5 {
    padding: 0;

    .ant-layout-has-sider {
      background-color: rgb(255 0 0 / 0%);
      border-radius: 12px;

      .ant-layout-sider {
        border-radius: 12px;
      }
    }
  }
}

.eval-plan-desig-layout-left {
  ::v-deep > .ant-layout-sider {
    height: calc(100vh - 130px);
  }

  ::v-deep .indicator-list-card-gather {
    height: calc(100vh - 180px);

    .indicator-tree-manager-card {
      height: calc(100vh - 350px);
    }

    .indicator_type_card {
      .ant-card-body {
        height: calc(100vh - 198px);
        overflow-y: auto;
      }
    }
  }
}

.middle-tree-div {
  ::v-deep .indicator-manager {
    width: calc(100% - 20px);
  }

  ::v-deep {
    .indicator_list,
    .indicator_weight {
      .main_card_customize_style_266 {
        height: calc(100vh - 210px) !important;
      }
    }
    //
  }
}

/* 确保tab内容区域能够正确滚动 */
</style>
