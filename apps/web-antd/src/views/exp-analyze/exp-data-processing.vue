<script setup lang="ts">
import { h, nextTick, onMounted, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';

import {
  CheckCircleFilled,
  ClockCircleOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import { expDesignExpTreeInfo } from '#/api/module2';
import {
  dataClean,
  deleteDeduceRecordCascade,
  getCollectionByDeduceRecordId,
  getSampleData,
  pageDeduceRecord,
} from '#/api/module3';
import {converDate} from "#/utils/date";

// 想定方案树数据
const scenarioTreeData = ref([]);
// 选中的节点 - 改为单选
const selectedNode = ref(null);
// 选中节点的key - 改为单选
const selectedKey = ref(null);
// 当前选中的节点类型 ('scenario' | 'scheme' | 'experiment' | null)
const selectedNodeType = ref(null);
// 当前选中的实验类型 (1-单装实验, 2-作战单元/战术行动实验)
const activeExpType = ref(1);
// 加载状态
const loading = ref(false);
// 展开的节点keys
const expandedKeys = ref([]);
// 训练记录数据
const tableDataSample = ref([]);
// 训练记录加载状态
const tableLoading = ref(false);
// 选中的训练记录 - 改为多选
const selectedTrainRecords = ref([]);
const selectedTrainRecordKeys = ref([]);
// 搜索条件
const searchFilters = ref({
  samplingMethod: null, // 抽样方法
  trainType: null, // 训练类型
});
// 过滤后的训练记录数据
const filteredTableData = ref([]);
// 数据清洗相关
const cleaningVisible = ref(false);
const cleaningProgress = ref([]);
const cleaningLoading = ref(false);
// 分页信息
const pagination = ref({
  current: 1,
  pageSize: 15,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
  pageSizeOptions: ['10', '15', '20', '50', '100'],
});

// 构建树数据
const buildTreeData = (treeData) => {
  return treeData.map((scenario) => ({
    title: scenario.scenarioName,
    key: `scenario-${scenario.id}`,
    type: 'schemeInfo',
    data: scenario,
    selectable: true,
    children:
      scenario.schemeInfoList?.map((scheme) => ({
        title: scheme.schemeName,
        key: `scheme-${scheme.id}`,
        type: 'scheme',
        data: scheme,
        selectable: true,
        children:
          scheme.expInfoList?.map((exp) => ({
            title: exp.expName,
            key: `experiment-${exp.id}`,
            type: 'experiment',
            data: exp,
            selectable: true,
            children: [],
          })) || [],
      })) || [],
  }));
};

// 收集所有节点的key用于展开
const collectAllKeys = (nodes) => {
  const keys = [];
  const traverse = (nodeList) => {
    nodeList.forEach((node) => {
      keys.push(node.key);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
  return keys;
};

// 加载想定方案数据
const loadScenarioData = async () => {
  try {
    loading.value = true;

    // 调用新的接口获取实验树信息，传入type参数
    const treeDataRes = await expDesignExpTreeInfo(activeExpType.value);

    // 构建树数据
    const treeData = buildTreeData(treeDataRes || []);
    scenarioTreeData.value = treeData;

    // 收集所有节点的key并设置为展开状态
    expandedKeys.value = collectAllKeys(treeData);
  } catch {
    // 加载想定方案数据失败
  } finally {
    loading.value = false;
  }
};
// 处理节点展开
const onExpand = (keys) => {
  expandedKeys.value = keys;
};

// 处理实验类型切换
const handleExpTypeChange = (newType) => {
  activeExpType.value = newType;

  // 清空选中状态
  selectedNode.value = null;
  selectedKey.value = null;
  selectedNodeType.value = null;

  // 清空训练记录相关数据
  tableDataSample.value = [];
  filteredTableData.value = [];
  selectedTrainRecords.value = [];
  selectedTrainRecordKeys.value = [];

  // 重置搜索条件
  searchFilters.value.samplingMethod = null;
  searchFilters.value.trainType = null;

  // 重置分页
  pagination.value.current = 1;
  pagination.value.total = 0;

  // 重新加载想定方案数据
  loadScenarioData();
};

// 处理节点选择 - 改为单选
const handleNodeSelect = (selectedKeysValue, { selected, node }) => {
  if (selected) {
    // 单选模式，只能选择一个节点
    selectedNode.value = node;
    selectedKey.value = node.key;
    selectedNodeType.value = node.type;

    // 切换节点时，清空之前的数据
    tableDataSample.value = [];
    filteredTableData.value = [];
    selectedTrainRecords.value = [];
    selectedTrainRecordKeys.value = [];

    // 重置搜索条件
    searchFilters.value.samplingMethod = null;
    searchFilters.value.trainType = null;

    // 重置分页
    pagination.value.current = 1;
    pagination.value.total = 0;

    // 加载训练记录
    loadTrainRecords();
  } else {
    // 取消选择
    selectedNode.value = null;
    selectedKey.value = null;
    selectedNodeType.value = null;
    tableDataSample.value = [];
    filteredTableData.value = [];
    selectedTrainRecords.value = [];
    selectedTrainRecordKeys.value = [];

    // 重置搜索条件
    searchFilters.value.samplingMethod = null;
    searchFilters.value.trainType = null;

    // 重置分页
    pagination.value.current = 1;
    pagination.value.total = 0;
  }
};

// 注释掉样本表格相关的处理函数，因为不再使用样本表格
// // 获取当前表格的所有数据keys（用于判断某个key是否属于当前表格）
// const getCurrentTableKeys = (methodData) => {
//  return methodData.samples.map((sample) => sample.key);
// };

// // 处理样本表格行选择 - 支持跨表格选择
// const handleSampleRowSelect = (selectedRowKeys, selectedRows, methodData) => {
//  // 样本行选择变化//  });

//  // 获取当前表格的所有可能keys
//  const currentTableKeys = getCurrentTableKeys(methodData);

//  // 从全局选中状态中移除当前表格的所有项
//  const otherTableSelectedKeys = selectedSampleKeys.value.filter(
//   (key) => !currentTableKeys.includes(key),
//  );
//  const otherTableSelectedRows = selectedSampleNodes.value.filter(
//   (row) => !currentTableKeys.includes(row.key),
//  );

//  // 合并其他表格的选中项和当前表格的新选中项
//  const newSelectedKeys = [...otherTableSelectedKeys, ...selectedRowKeys];
//  const newSelectedRows = [...otherTableSelectedRows, ...selectedRows];

//  selectedSampleKeys.value = newSelectedKeys;
//  selectedSampleNodes.value = newSelectedRows;

//  // 更新后的选中状态

//  // 重置分页到第一页
//  pagination.value.current = 1;

//  // 加载训练记录
//  loadTrainRecords();
// };

// const sampleTableColumns = [
//  {
//   title: '序号',
//   dataIndex: 'index',
//   width: 80,
//   align: 'center',
//  },
//  {
//   title: '样本ID',
//   dataIndex: 'sampleId',
//   width: 120,
//  },
// ];

// 加载训练记录
const loadTrainRecords = async () => {
  // 如果没有选中节点，清空训练记录
  if (!selectedNode.value) {
    tableDataSample.value = [];
    filteredTableData.value = [];
    pagination.value.total = 0;
    selectedTrainRecords.value = [];
    selectedTrainRecordKeys.value = [];
    return;
  }

  try {
    tableLoading.value = true;

    // 构建请求参数
    const requestData = {
      pageNumber: pagination.value.current,
      pageSize: pagination.value.pageSize,
      data: {},
    };

    // 根据节点类型添加不同的参数
    switch (selectedNode.value.type) {
      case 'experiment': {
        // 实验节点：传scenarioId、schemeId和expId
        requestData.data.scenarioId = selectedNode.value.data.scenarioId;
        requestData.data.schemeId = selectedNode.value.data.schemeId;
        requestData.data.expId = selectedNode.value.data.id;
        break;
      }
      case 'scheme': {
        // 方案节点：传scenarioId和schemeId
        requestData.data.scenarioId = selectedNode.value.data.scenarioId;
        requestData.data.schemeId = selectedNode.value.data.id;
        break;
      }
      case 'schemeInfo': {
        // 想定节点：传scenarioId
        requestData.data.scenarioId = selectedNode.value.data.id;
        break;
      }
      // No default
    }

    // 添加搜索条件参数
    if (searchFilters.value.samplingMethod !== null) {
      requestData.data.type = searchFilters.value.samplingMethod;
    }
    if (searchFilters.value.trainType !== null) {
      requestData.data.trainType = searchFilters.value.trainType;
    }

    const response = await pageDeduceRecord(requestData);

    if (response) {
      // 直接使用后端返回的数据，不再需要前端过滤
      tableDataSample.value = (response.records || []).map((record) => ({
        ...record,
        cleanStatus: record.cleanStatus || 0, // 0-未清理, 1-已清洗
      }));

      // 直接设置为表格数据，不再需要前端过滤
      filteredTableData.value = tableDataSample.value;

      pagination.value.total = Number(response.total) || 0;
      pagination.value.current =
        Number(response.current) || pagination.value.current;
      // 更新分页信息
    } else {
      tableDataSample.value = [];
      filteredTableData.value = [];
      pagination.value.total = 0;
    }

    // 清空选中的记录
    selectedTrainRecords.value = [];
    selectedTrainRecordKeys.value = [];
  } catch {
    // 加载训练记录失败
    tableDataSample.value = [];
    filteredTableData.value = [];
    pagination.value.total = 0;
  } finally {
    tableLoading.value = false;
  }
};

// 监听搜索条件变化，自动进行查询
watch(
  () => [searchFilters.value.samplingMethod, searchFilters.value.trainType],
  () => {
    // 只有在有选中节点的情况下才重新查询
    if (selectedNode.value) {
      // 重置分页到第一页
      pagination.value.current = 1;
      // 重新加载训练记录
      loadTrainRecords();
    }
  },
  { deep: true },
);

// 处理分页变化
const handlePaginationChange = (current: number, pageSize?: number) => {
  pagination.value.current = current;
  if (pageSize) {
    pagination.value.pageSize = pageSize;
  }
  loadTrainRecords();

  // 滚动到表格头部
  nextTick(() => {
    const tableContainer = document.querySelector('.ant-table-container');
    if (tableContainer) {
      tableContainer.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  });
};

// 处理训练记录选择 - 改为多选
const handleTrainRecordSelect = (selectedRowKeys, selectedRows) => {
  selectedTrainRecordKeys.value = selectedRowKeys;
  selectedTrainRecords.value = selectedRows;
};

// 删除训练记录
const handleDeleteTrainRecord = async () => {
  if (selectedTrainRecords.value.length === 0) {
    message.warning('请先选择要删除的训练记录');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedTrainRecords.value.length} 条训练记录吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 批量删除
        await Promise.all(
          selectedTrainRecords.value.map((record) =>
            deleteDeduceRecordCascade(record.id),
          ),
        );
        message.success('删除成功');
        selectedTrainRecords.value = [];
        selectedTrainRecordKeys.value = [];
        // 重新加载训练记录
        loadTrainRecords();
      } catch {
        // 删除训练记录失败
        message.error('删除失败');
      }
    },
  });
};

// 数据清洗功能
const handleDataCleaning = () => {
  if (selectedTrainRecords.value.length === 0) {
    message.warning('请先选择要清洗的训练记录');
    return;
  }

  Modal.confirm({
    title: '确认清洗',
    content: `确定要对选中的 ${selectedTrainRecords.value.length} 条训练记录进行数据清洗吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      startDataCleaning();
    },
  });
};

// 开始数据清洗
const startDataCleaning = () => {
  // 初始化清洗进度
  cleaningProgress.value = selectedTrainRecords.value.map((record) => {
    // 构建显示名称：样本ID-训练模式-训练类型-创建时间
    const sampleId = record.sampleId || '未知';
    const trainModeMap = { 1: '单装实验', 2: '作战单元/战术行动实验' };
    const trainMode = trainModeMap[record.trainMode] || '未知';
    const trainTypeMap = { 1: 'AI自动推演', 2: '人在环', 3: '批量推演' };
    const trainType = trainTypeMap[record.trainType] || '未知';

    // 处理创建时间格式
    let createTime = '未知时间';
    if (record.createTime) {
      try {
        // 如果是时间戳，格式化为可读格式
        if (
          typeof record.createTime === 'number' ||
          /^\d+$/.test(record.createTime)
        ) {
          createTime = dayjs(Number(record.createTime)).format('MM-DD HH:mm');
        } else {
          // 如果已经是格式化的时间字符串，直接使用
          createTime = record.createTime;
        }
      } catch {
        // 时间格式化失败
        createTime = record.createTime;
      }
    }

    const displayName = `${sampleId}-${trainMode}-${trainType}-${createTime}`;

    return {
      id: record.id,
      name: displayName,
      progress: 0,
      status: 'processing', // processing, success, error
    };
  });

  cleaningVisible.value = true;
  cleaningLoading.value = true;

  // 模拟清洗过程
  selectedTrainRecords.value.forEach((record, index) => {
    simulateCleaningProgress(record.id, index);
  });
};

// 模拟清洗进度
const simulateCleaningProgress = (recordId, _index) => {
  const duration = Math.random() * 2000 + 1000; // 1-3秒随机时长
  const steps = 18; // 进度条分18步，到90%
  const stepDuration = duration / steps;

  let currentStep = 0;

  const timer = setInterval(() => {
    currentStep++;
    const progressItem = cleaningProgress.value.find(
      (item) => item.id === recordId,
    );
    if (progressItem) {
      // 进度到90%
      progressItem.progress = Math.min((currentStep / steps) * 90, 90);

      if (currentStep >= steps) {
        clearInterval(timer);
        // 调用清洗接口
        callCleaningAPI(recordId);
      }
    }
  }, stepDuration);
};

// 调用清洗接口
const callCleaningAPI = async (recordId) => {
  try {
    const progressItem = cleaningProgress.value.find(
      (item) => item.id === recordId,
    );
    if (progressItem) {
      // 调用数据清洗接口
      await dataClean(recordId);

      // 接口成功，进度直接到100%
      progressItem.progress = 100;
      progressItem.status = 'success';

      // 更新原始数据中的清洗状态
      const originalRecord = tableDataSample.value.find(
        (record) => record.id === recordId,
      );
      if (originalRecord) {
        originalRecord.cleanStatus = 1; // 已清洗
      }

      // 检查是否所有清洗都完成
      const allCompleted = cleaningProgress.value.every(
        (item) => item.status === 'success',
      );
      if (allCompleted) {
        setTimeout(() => {
          cleaningLoading.value = false;
          message.success('数据清洗完成');
          // 重新加载训练记录列表
          loadTrainRecords();
          setTimeout(() => {
            cleaningVisible.value = false;
          }, 1000);
        }, 500);
      }
    }
  } catch {
    // 数据清洗失败
    const progressItem = cleaningProgress.value.find(
      (item) => item.id === recordId,
    );
    if (progressItem) {
      progressItem.status = 'error';
      progressItem.progress = 90; // 保持在90%
      // 使用进度项的显示名称来显示错误信息
      message.error(`${progressItem.name} 清洗失败`);
    } else {
      message.error(`训练记录 ${recordId} 清洗失败`);
    }
  }
};

// 查看详情
const handleViewDetail = async (record) => {
  try {
    detailLoading.value = true;
    currentDeduceRecordId.value = record.id;

    // 调用接口获取采集数据
    const collectionData = await getCollectionByDeduceRecordId(record.id);

    if (
      collectionData &&
      Array.isArray(collectionData) &&
      collectionData.length > 0
    ) {
      // 将采集数据转换为tabs
      collectionTabs.value = collectionData.map((item) => ({
        key: item.tableName,
        tab: item.name,
        tableName: item.tableName,
        count: item.count,
        capacityCollectionId: item.capacityCollectionId,
      }));

      // 为每个tab初始化分页信息
      tabPaginationMap.value.clear();
      collectionTabs.value.forEach((tab) => {
        tabPaginationMap.value.set(tab.key, {
          current: 1,
          pageSize: 10,
          total: 0,
        });
      });

      // 默认选中第一个tab
      activeTabKey.value = collectionTabs.value[0].key;

      // 加载第一个tab的数据
      await loadTabData(collectionTabs.value[0].tableName);

      detailVisible.value = true;
    } else {
      message.warning('该训练记录暂无采集数据');
    }
  } catch {
    // 获取训练记录详情失败
    message.error('该训练记录暂无采集数据');
  } finally {
    detailLoading.value = false;
  }
};

// 切换tab时加载数据
const handleTabChange = async (activeKey) => {
  activeTabKey.value = activeKey;
  await loadTabData(activeKey);
};

// 处理tab内分页变化
const handleTabTableChange = async (current: number, pageSize?: number) => {
  const currentPagination = tabPaginationMap.value.get(activeTabKey.value);
  if (currentPagination) {
    currentPagination.current = current;
    currentPagination.pageSize = pageSize || currentPagination.pageSize;
    await loadTabData(activeTabKey.value);

    // 滚动到表格头部
    scrollToTableTop();
  }
};

// 滚动到表格头部
const scrollToTableTop = () => {
  // 使用 nextTick 确保DOM更新完成后再滚动
  nextTick(() => {
    try {
      // 查找当前打开的modal（训练记录详情modal）
      let modal = null;

      // 方法1：通过modal标题查找
      const modalTitles = document.querySelectorAll('.ant-modal-title');
      for (const title of modalTitles) {
        if (title.textContent && title.textContent.includes('训练记录详情')) {
          modal = title.closest('.ant-modal');
          break;
        }
      }

      // 方法2：如果方法1失败，查找当前显示的modal
      if (!modal) {
        modal = document.querySelector('.ant-modal:not(.ant-modal-hidden)');
      }

      if (!modal) {
        // 未找到训练记录详情modal
        return;
      }

      // 在找到的modal内查找当前活动的tab内容
      const activeTabPane = modal.querySelector('.ant-tabs-tabpane-active');
      if (activeTabPane) {
        // 查找tab内的表格滚动容器（使用更精确的选择器）
        const scrollContainer = activeTabPane.querySelector(
          '.detail-table-container',
        );
        if (scrollContainer) {
          // 平滑滚动到顶部
          scrollContainer.scrollTo({
            top: 0,
            behavior: 'smooth',
          });
          // 已滚动到表格顶部
        } else {
          // 降级到通用的滚动容器
          const fallbackContainer =
            activeTabPane.querySelector('.overflow-y-auto');
          if (fallbackContainer) {
            fallbackContainer.scrollTo({
              top: 0,
              behavior: 'smooth',
            });
            // 使用降级选择器滚动到表格顶部
          } else {
            // 未找到滚动容器
          }
        }
      } else {
        // 未找到活动的tab面板
      }
    } catch {
      // 滚动到表格顶部失败
      // 降级方案：尝试滚动modal body
      try {
        const modalBody = document.querySelector('.ant-modal-body');
        if (modalBody) {
          modalBody.scrollTop = 0;
          // 使用降级方案滚动到modal顶部
        }
      } catch {
        // 降级方案也失败了
      }
    }
  });
};

// 加载tab数据
const loadTabData = async (tableName) => {
  if (!tableName || !currentDeduceRecordId.value) return;

  // 获取当前tab的分页信息
  const currentPagination = tabPaginationMap.value.get(tableName);
  if (!currentPagination) return;

  try {
    tableDataLoading.value = true;

    const response = await getSampleData({
      pageNumber: currentPagination.current, // 从1开始
      pageSize: currentPagination.pageSize,
      data: {
        tableName,
        deduceRecordId: currentDeduceRecordId.value,
      },
    });

    if (response) {
      // 动态生成表格列
      if (response.columns && Array.isArray(response.columns)) {
        currentTableColumns.value = response.columns.map((col) => ({
          title: col.columnComment || col.columnName,
          dataIndex: col.columnName,
          key: col.columnName,
          width: 150,
        }));
      } else {
        message.warning('无采集数据');
      }

      // 设置表格数据
      currentTableData.value = response.data || [];

      // 更新分页信息
      currentPagination.total = Number(response.total) || 0;
      currentTotal.value = Number(response.total) || 0;
    } else {
      currentTableColumns.value = [];
      currentTableData.value = [];
      currentPagination.total = 0;
      currentTotal.value = 0;
    }
  } catch {
    // 加载tab数据失败
    message.error('加载数据失败');
    currentTableColumns.value = [];
    currentTableData.value = [];
    if (currentPagination) {
      currentPagination.total = 0;
    }
    currentTotal.value = 0;
  } finally {
    tableDataLoading.value = false;
  }
};

const tableDataColumns = [
  {
    title: '想定名称',
    dataIndex: 'scenarioName',
    width: 150,
    fixed: 'left',
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '方案名称',
    dataIndex: 'schemeName',
    width: 150,
    fixed: 'left',
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '实验名称',
    dataIndex: 'expName',
    width: 150,
    fixed: 'left',
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '抽样方法',
    dataIndex: 'type',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      const typeMap = {
        1: '正交抽样',
        2: '拉丁方抽样',
        3: '完全抽样',
        4: '均匀抽样',
      };
      return typeMap[text] || text || '-';
    },
  },
  {
    title: '时间戳',
    dataIndex: 'timeStamp',
    width: 180,
    align: 'center',
    customRender: ({ text }) => {
      if (!text) return '-';
      return converDate(text);
    },
  },
  {
    title: '训练模式',
    dataIndex: 'trainMode',
    width: 100,
    align: 'center',
    customRender: ({ text }) => {
      const modeMap = { 1: '单装实验', 2: '作战单元/战术行动实验' };
      return modeMap[text] || text;
    },
  },
  {
    title: '训练类型',
    dataIndex: 'trainType',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      const typeMap = { 1: 'AI自动推演', 2: '人在环', 3: '批量推演' };
      return typeMap[text] || text;
    },
  },
  {
    title: '样本ID',
    dataIndex: 'sampleId',
    width: 100,
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '是否采集',
    dataIndex: 'isCollection',
    width: 100,
    align: 'center',
    customRender: ({ text }) => (text === 1 ? '是' : '否'),
  },
  {
    title: '是否录制',
    dataIndex: 'isPlayback',
    width: 100,
    align: 'center',
    customRender: ({ text }) => (text === 1 ? '是' : '否'),
  },
  {
    title: '倍速',
    dataIndex: 'multiSpeed',
    width: 80,
    align: 'center',
  },
  {
    title: '运行次数',
    dataIndex: 'operationNum',
    width: 100,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    align: 'center',
  },
  {
    title: '清洗状态',
    dataIndex: 'cleanStatus',
    width: 100,
    align: 'center',
    customRender: ({ text }) => {
      if (text === 1) {
        // 已清洗 - 绿色对勾图标
        return h('div', { class: 'flex items-center justify-center' }, [
          h(CheckCircleFilled, {
            style: { color: '#52c41a', fontSize: '16px' },
          }),
          h('span', { class: 'ml-1 text-sm' }, '已清洗'),
        ]);
      } else {
        // 未清理 - 灰色时钟图标
        return h('div', { class: 'flex items-center justify-center' }, [
          h(ClockCircleOutlined, {
            style: { color: '#d9d9d9', fontSize: '16px' },
          }),
          h('span', { class: 'ml-1 text-sm text-gray-500' }, '未清理'),
        ]);
      }
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    fixed: 'right',
    align: 'center',
  },
];

const detailVisible = ref(false);
// 查看详情相关数据
const detailLoading = ref(false);
const collectionTabs = ref([]);
const activeTabKey = ref('');
const currentTableData = ref([]);
const currentTableColumns = ref([]);
const currentDeduceRecordId = ref(null);
const tableDataLoading = ref(false);
// 每个tab的分页信息
const tabPaginationMap = ref(new Map());
const currentTotal = ref(0);
// 全屏状态
const isFullscreen = ref(false);

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
};

// 组件挂载时加载数据
onMounted(() => {
  loadScenarioData();
});
</script>

<template>
  <Page auto-content-height content-class="flex flex-col">
    <div class="processing-wrapper flex h-[calc(100%-50px)] gap-4">
      <div class="w-[300px]">
        <a-card
          class="flex h-full flex-col"
          :body-style="{
            padding: '0',
            flex: '1',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }"
        >
          <a-tabs
            v-model:active-key="activeExpType"
            @change="handleExpTypeChange"
            class="flex h-full flex-1 flex-col"
            :tab-bar-style="{ marginBottom: 0, padding: '8px 12px 0' }"
          >
            <a-tab-pane
              :key="1"
              tab="单装实验"
              class="flex flex-1 flex-col overflow-y-auto"
            >
              <div class="flex-1 p-3" v-loading="loading">
                <a-tree
                  :tree-data="scenarioTreeData"
                  :field-names="{
                    title: 'title',
                    key: 'key',
                    children: 'children',
                  }"
                  :expanded-keys="expandedKeys"
                  :selected-keys="selectedKey ? [selectedKey] : []"
                  @expand="onExpand"
                  @select="handleNodeSelect"
                />
              </div>
            </a-tab-pane>
            <a-tab-pane
              :key="2"
              tab="作战单元/战术行动实验"
              class="flex flex-1 flex-col overflow-y-auto"
            >
              <div class="flex-1 p-3" v-loading="loading">
                <a-tree
                  :tree-data="scenarioTreeData"
                  :field-names="{
                    title: 'title',
                    key: 'key',
                    children: 'children',
                  }"
                  :expanded-keys="expandedKeys"
                  :selected-keys="selectedKey ? [selectedKey] : []"
                  @expand="onExpand"
                  @select="handleNodeSelect"
                />
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </div>
      <a-card
        title="训练记录"
        class="flex h-full min-w-0 flex-1 flex-col"
        :body-style="{
          padding: '12px',
          flex: '1',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'auto',
        }"
      >
        <div class="mb-3 flex gap-4">
          <div class="flex items-center gap-2">
            <span class="text-sm">抽样方法:</span>
            <a-select
              v-model:value="searchFilters.samplingMethod"
              placeholder="请选择抽样方法"
              style="width: 150px"
              allow-clear
            >
              <a-select-option :value="1">正交抽样</a-select-option>
              <a-select-option :value="2">拉丁方抽样</a-select-option>
              <a-select-option :value="3">完全抽样</a-select-option>
              <a-select-option :value="4">均匀抽样</a-select-option>
            </a-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-sm">训练类型:</span>
            <a-select
              v-model:value="searchFilters.trainType"
              placeholder="请选择训练类型"
              style="width: 150px"
              allow-clear
            >
              <a-select-option :value="1">AI自动推演</a-select-option>
              <a-select-option :value="2">人在环</a-select-option>
              <a-select-option :value="3">批量推演</a-select-option>
            </a-select>
          </div>
        </div>

        <div class="flex flex-1 flex-col overflow-hidden">
          <div class="flex-1 overflow-auto">
            <a-table
              :data-source="filteredTableData"
              :columns="tableDataColumns"
              :pagination="false"
              :loading="tableLoading"
              :row-selection="{
                type: 'checkbox',
                selectedRowKeys: selectedTrainRecordKeys,
                onChange: handleTrainRecordSelect,
              }"
              row-key="id"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'action'">
                  <a-button
                    type="link"
                    :loading="
                      detailLoading && currentDeduceRecordId === record.id
                    "
                    @click="handleViewDetail(record)"
                  >
                    查看详情
                  </a-button>
                </template>
              </template>
            </a-table>
          </div>
          <div class="flex-shrink-0 pt-3">
            <a-pagination
              v-model:current="pagination.current"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              :show-size-changer="pagination.showSizeChanger"
              :show-quick-jumper="pagination.showQuickJumper"
              :show-total="pagination.showTotal"
              :page-size-options="pagination.pageSizeOptions"
              class="text-right"
              @change="handlePaginationChange"
              @show-size-change="handlePaginationChange"
            />
          </div>
        </div>
      </a-card>
    </div>
    <div class="my-2 flex items-center justify-center gap-2">
      <a-button
        type="primary"
        :disabled="selectedTrainRecords.length === 0"
        @click="handleDataCleaning"
      >
        数据清洗
      </a-button>
      <a-button
        type="primary"
        danger
        :disabled="selectedTrainRecords.length === 0"
        @click="handleDeleteTrainRecord"
        ghost
      >
        删除
      </a-button>
    </div>
    <a-modal
      v-model:open="detailVisible"
      :width="isFullscreen ? '100%' : '80%'"
      :footer="null"
      :body-style="{
        padding: '0',
        maxHeight: isFullscreen ? 'calc(100vh - 75px)' : '70vh',
        overflow: 'hidden',
      }"
      :wrap-class-name="isFullscreen ? 'full-modal' : ''"
      :centered="!isFullscreen"
    >
      <template #title>
        <div class="flex items-start justify-between">
          <span>训练记录详情</span>
          <div class="absolute right-[60px] top-[18px] flex items-center gap-2">
            <section
              class="fullscreen-btn cursor-pointer"
              @click="toggleFullscreen"
              :title="isFullscreen ? '退出全屏' : '全屏显示'"
            >
              <FullscreenExitOutlined v-if="isFullscreen" />
              <FullscreenOutlined v-else />
            </section>
          </div>
        </div>
      </template>
      <a-card
        :body-style="{
          padding: '12px',
          maxHeight: isFullscreen ? 'calc(100vh - 75px)' : '70vh',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        }"
      >
        <a-tabs
          v-model:active-key="activeTabKey"
          class="flex-1"
          :tab-bar-style="{ marginBottom: '16px' }"
          @change="handleTabChange"
        >
          <a-tab-pane
            v-for="tab in collectionTabs"
            :key="tab.key"
            :tab="`${tab.tab} (${tab.count})`"
            class="flex flex-col"
            :style="{
              height: isFullscreen
                ? 'calc(100vh - 170px)'
                : 'calc(70vh - 120px)',
            }"
          >
            <div
              class="detail-table-container flex-1 overflow-y-auto"
              style="min-height: 0"
            >
              <a-table
                :data-source="currentTableData"
                :columns="currentTableColumns"
                :loading="tableDataLoading"
                :pagination="false"
                :scroll="{ x: 'max-content' }"
                sticky
              />
            </div>
            <div class="flex-shrink-0 py-3">
              <a-pagination
                :current="tabPaginationMap.get(activeTabKey)?.current || 1"
                :page-size="tabPaginationMap.get(activeTabKey)?.pageSize || 10"
                :total="currentTotal || 0"
                :show-size-changer="true"
                :show-quick-jumper="true"
                :show-total="
                  (total: number, range: [number, number]) =>
                    `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
                "
                :page-size-options="['10', '20', '50', '100']"
                class="text-center"
                @change="handleTabTableChange"
                @show-size-change="handleTabTableChange"
              />
            </div>
          </a-tab-pane>
        </a-tabs>

        <div
          v-if="collectionTabs.length === 0 && !detailLoading"
          class="flex h-32 items-center justify-center text-gray-500"
        >
          暂无采集数据
        </div>

        <div v-if="detailLoading" class="flex h-32 items-center justify-center">
          <a-spin size="large" />
        </div>
      </a-card>
    </a-modal>

    <a-modal
      v-model:open="cleaningVisible"
      title="数据清洗进度"
      width="800px"
      :footer="null"
      :closable="!cleaningLoading"
      :mask-closable="false"
    >
      <div class="space-y-4">
        <div v-for="item in cleaningProgress" :key="item.id" class="space-y-2">
          <div class="flex items-start justify-between gap-2">
            <span class="flex-1 break-all text-sm" :title="item.name">{{
              item.name
            }}</span>
            <span class="flex-shrink-0 text-sm text-gray-500">{{ Math.round(item.progress) }}%</span>
          </div>
          <a-progress
            :percent="item.progress"
            :status="
              item.status === 'success'
                ? 'success'
                : item.status === 'error'
                  ? 'exception'
                  : 'active'
            "
            :stroke-color="
              item.status === 'success'
                ? '#52c41a'
                : item.status === 'error'
                  ? '#ff4d4f'
                  : '#1890ff'
            "
          />
        </div>
        <div v-if="!cleaningLoading" class="mt-4 text-center">
          <a-button type="primary" @click="cleaningVisible = false">
            关闭
          </a-button>
        </div>
      </div>
    </a-modal>
  </Page>
</template>
<style scoped lang="scss">
.processing-wrapper {
  ::v-deep(.ant-tabs-content) {
    height: 100%;
  }

  .sample-list {
    ::v-deep(.ant-collapse-header) {
      padding: 5px 0;
    }
  }
}
</style>
