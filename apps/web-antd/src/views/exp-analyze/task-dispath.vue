<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { message, Modal } from 'ant-design-vue';

import { evaluateTaskGo } from '#/api/evaluateTaskGo';
import { currentOnline } from '#/api/module3';
import { dispatchTask, listAllTasks } from '#/api/module4';

// 定义接口类型
interface TaskOption {
  id: number;
  name: string;
  dispatched: number; // 0-未下发 1-已下发
}

interface IpOption {
  value: string;
  label: string;
}

interface OnlineUser {
  userId: number;
  uuid: string;
  username: string;
  ip: string;
}

// 定义props
const props = defineProps<{
  selectedTask?: any; // 当前选中的任务
}>();

// 定义emit事件
const emit = defineEmits(['success', 'cancel']);

// 表单数据
const formData = ref({
  evaluateTaskId: undefined as number | undefined,
  ip: undefined as string | undefined,
});

// 数据状态
const taskOptions = ref<TaskOption[]>([]);
const ipOptions = ref<IpOption[]>([]);
const taskLoading = ref(false);
const ipLoading = ref(false);
const submitLoading = ref(false);

// 任务搜索关键词
const taskSearchValue = ref('');

// 过滤后的任务选项
const filteredTaskOptions = ref<TaskOption[]>([]);

// 加载所有任务
const loadAllTasks = async () => {
  try {
    taskLoading.value = true;
    const response = await listAllTasks();
    // 只保留未下发的任务（dispatched = 0）
    const undispatchedTasks = (response || []).filter(
      (task: TaskOption) => task.dispatched === 0,
    );
    taskOptions.value = undispatchedTasks;
    filteredTaskOptions.value = undispatchedTasks;
  } catch (error) {
    console.error('加载任务列表失败:', error);
    message.error('加载任务列表失败');
  } finally {
    taskLoading.value = false;
  }
};

// 加载在线IP
const loadOnlineIps = async (isManualRefresh = false) => {
  try {
    ipLoading.value = true;

    // 如果是手动刷新，先保存当前选中的选项信息
    let previousSelectedOption: IpOption | undefined;
    if (isManualRefresh && formData.value.ip) {
      previousSelectedOption = ipOptions.value.find(
        (option) => option.value === formData.value.ip,
      );
    }

    const response = await currentOnline();
    let onlineUsers: OnlineUser[] = [];

    // 根据实际接口返回数据结构处理
    if (Array.isArray(response)) {
      onlineUsers = response;
    } else if (response && Array.isArray(response.data)) {
      onlineUsers = response.data;
    }

    // 转换为选项格式
    ipOptions.value = onlineUsers.map((user: OnlineUser) => ({
      value: user.ip,
      label: `${user.username} (${user.ip})`,
    }));

    // 如果是手动刷新（点击在线检测），检查当前选中的IP是否还在线
    if (isManualRefresh && formData.value.ip) {
      const currentSelectedIp = formData.value.ip;
      const isStillOnline = onlineUsers.some(
        (user) => user.ip === currentSelectedIp,
      );

      if (!isStillOnline) {
        // 使用之前保存的选项信息
        const displayName = previousSelectedOption?.label || currentSelectedIp;

        // 清空选择并提示用户
        formData.value.ip = undefined;
        message.warning(`${displayName} 已离线，请重新选择`);
      }
    }
  } catch (error) {
    console.error('加载在线IP失败:', error);
    message.error('加载在线IP失败');
    ipOptions.value = [];
  } finally {
    ipLoading.value = false;
  }
};

// 任务搜索过滤
const handleTaskSearch = (value: string) => {
  taskSearchValue.value = value;
  filteredTaskOptions.value = value
    ? taskOptions.value.filter((task) =>
        task.name.toLowerCase().includes(value.toLowerCase()),
      )
    : taskOptions.value;
};

// 在线检测
const handleOnlineDetection = () => {
  loadOnlineIps(true); // 传递true表示这是手动刷新
};

// 分配任务
const handleDispatch = async () => {
  if (!formData.value.evaluateTaskId) {
    message.error('请选择任务');
    return;
  }
  if (!formData.value.ip) {
    message.error('请选择IP');
    return;
  }

  // 获取选中的任务名称用于确认提示
  const selectedTask = taskOptions.value.find(
    (task) => task.id === formData.value.evaluateTaskId,
  );
  const taskName = selectedTask?.name || '未知任务';

  // 获取选中的IP对应的用户信息
  const selectedIpOption = ipOptions.value.find(
    (option) => option.value === formData.value.ip,
  );
  const ipDisplayName = selectedIpOption?.label || formData.value.ip;

  // 显示确认对话框
  Modal.confirm({
    title: '确认任务分配',
    content: `确定要将任务"${taskName}"分配到${ipDisplayName}吗？\n\n注意：任务一旦被分配则无法编辑，请确认后再进行操作。`,
    okText: '确认分配',
    cancelText: '取消',
    okType: 'primary',
    width: 450,
    onOk: async () => {
      try {
        submitLoading.value = true;
        const response = await dispatchTask({
          evaluateTaskId: formData.value.evaluateTaskId!,
          ip: formData.value.ip!,
        });
        //  发送评估任务推演服务请求,对返回值无感知 todo,按理说可以监听了
        evaluateTaskGo(response);
        message.success('任务下发成功');

        // 清空表单数据
        formData.value.evaluateTaskId = undefined;
        formData.value.ip = undefined;

        // 重新加载任务列表，移除已下发的任务
        await loadAllTasks();

        emit('success');
      } catch (error) {
        console.error('任务下发失败:', error);
        message.error('任务下发失败');
      } finally {
        submitLoading.value = false;
      }
    },
  });
};

// 处理默认选择任务
const handleDefaultTaskSelection = () => {
  if (!props.selectedTask) return;

  // 检查当前选中的任务是否已下发
  if (props.selectedTask.dispatched === 1) {
    message.warning(`任务"${props.selectedTask.name}"已经下发，无法重复下发`);
    return;
  }

  // 如果任务未下发，检查是否在可选列表中
  const availableTask = taskOptions.value.find(
    (task) => task.id === props.selectedTask.id,
  );
  if (availableTask) {
    formData.value.evaluateTaskId = availableTask.id;
    message.info(`已自动选择当前任务"${availableTask.name}"`);
  }
};

// 取消
const handleCancel = () => {
  emit('cancel');
};

// 组件挂载时加载数据
onMounted(async () => {
  await loadAllTasks();
  await loadOnlineIps();
  // 加载完成后处理默认选择
  handleDefaultTaskSelection();
});
</script>

<template>
  <div class="p-4">
    <a-form :model="formData" layout="vertical">
      <a-form-item label="选择任务" required>
        <a-select
          v-model:value="formData.evaluateTaskId"
          placeholder="请选择未下发的任务"
          show-search
          :filter-option="false"
          :loading="taskLoading"
          @search="handleTaskSearch"
          class="w-full"
          :not-found-content="
            taskLoading
              ? '加载中...'
              : filteredTaskOptions.length === 0
                ? '暂无可下发的任务'
                : '未找到匹配的任务'
          "
        >
          <a-select-option
            v-for="task in filteredTaskOptions"
            :key="task.id"
            :value="task.id"
          >
            {{ task.name }}
          </a-select-option>
        </a-select>
        <div
          v-if="!taskLoading && filteredTaskOptions.length === 0"
          class="mt-2 text-sm text-gray-500"
        >
          当前没有可下发的任务，所有任务都已下发或暂无任务数据
        </div>
      </a-form-item>

      <a-form-item label="选择IP" required>
        <a-select
          v-model:value="formData.ip"
          placeholder="请选择IP"
          :loading="ipLoading"
          class="w-full"
        >
          <a-select-option
            v-for="ip in ipOptions"
            :key="ip.value"
            :value="ip.value"
          >
            {{ ip.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>

    <div class="mt-6 flex justify-between">
      <a-button @click="handleOnlineDetection" :loading="ipLoading">
        在线检测
      </a-button>
      <div class="space-x-2">
        <a-button
          type="primary"
          @click="handleDispatch"
          :loading="submitLoading"
          :disabled="
            !formData.evaluateTaskId ||
            !formData.ip ||
            filteredTaskOptions.length === 0
          "
        >
          分配
        </a-button>
        <a-button @click="handleCancel"> 取消 </a-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.ant-select {
  width: 100%;
}
</style>
