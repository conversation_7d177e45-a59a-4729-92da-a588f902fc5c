<script setup lang="ts">
import { onMounted, provide, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import {
  expDesignGetExpFullInfo,
  expDesignPageExpInfo,
  expDesignScenarioAndSchemeInfo,
} from '#/api/module2';
import ExpDistributedDeduction from '#/views/exp-run/components/singleEquipComponent/exp-distributed-deduction.vue';

// 当前激活的标签页
const activeTab = ref('1');

// 左侧想定-方案树相关
const treeData = ref<any[]>([]);
const originalTreeData = ref<any[]>([]); // 原始树数据，用于搜索过滤
const treeLoading = ref(false);
const selectedKeys = ref<string[]>([]);
const expandedKeys = ref<string[]>([]);
const selectedScheme = ref<any>(null); // 选中的方案
const searchKeyword = ref('');

// 右侧实验管理列表相关
const expList = ref<any[]>([]);
const expTotal = ref(0);
const expCurrentPage = ref(1);
const expPageSize = ref(15);
const expLoading = ref(false);

// 搜索条件
const searchConditions = ref({
  expName: '',
  createDateRange: [] as any[],
  creator: '',
});

// 实验管理表格列配置
const expColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    align: 'center',
    customRender: ({ index }: { index: number }) => index + 1,
  },
  {
    title: '名称',
    dataIndex: 'expName',
    key: 'expName',
    align: 'center',
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    align: 'center',
  },
  {
    title: '创建人',
    dataIndex: 'account',
    key: 'account',
    width: 100,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    align: 'center',
  },
];

// 获取想定-方案树数据
const fetchTreeData = async () => {
  try {
    treeLoading.value = true;
    const type = activeTab.value; // 1单装 2作战行动
    const res = await expDesignScenarioAndSchemeInfo(type);

    // 转换为树形结构数据
    const treeDataResult = (res || []).map((scenario: any) => ({
      title: scenario.scenarioName,
      key: `scenario-${scenario.id}`,
      value: `scenario-${scenario.id}`,
      scenarioId: scenario.id,
      isLeaf: false,
      selectable: false, // 想定节点不可选择
      children: (scenario.schemeInfoList || []).map((scheme: any) => ({
        title: scheme.schemeName,
        key: `scheme-${scheme.id}`,
        value: `scheme-${scheme.id}`,
        schemeId: scheme.id,
        scenarioId: scenario.id,
        scenarioName: scenario.scenarioName,
        schemeName: scheme.schemeName,
        isLeaf: true,
        selectable: true, // 方案节点可选择
      })),
    }));

    originalTreeData.value = treeDataResult; // 保存原始数据
    treeData.value = treeDataResult;

    // 自动展开所有想定节点
    expandedKeys.value = treeDataResult.map((scenario: any) => scenario.key);

    // 如果没有选中的方案且有数据，自动选中第一个可选择的方案节点
    if (!selectedScheme.value && treeDataResult.length > 0) {
      for (const scenario of treeDataResult) {
        if (scenario.children && scenario.children.length > 0) {
          const firstScheme = scenario.children[0];
          selectedKeys.value = [firstScheme.key];
          selectedScheme.value = {
            schemeId: firstScheme.schemeId,
            scenarioId: firstScheme.scenarioId,
            scenarioName: firstScheme.scenarioName,
            schemeName: firstScheme.schemeName,
          };
          expCurrentPage.value = 1;
          fetchExpList();
          break;
        }
      }
    }
  } catch (error: any) {
    console.error('获取想定-方案树失败', error);
    message.error('获取想定-方案树失败');
  } finally {
    treeLoading.value = false;
  }
};

// 获取实验管理列表
const fetchExpList = async () => {
  if (!selectedScheme.value) {
    expList.value = [];
    expTotal.value = 0;
    return;
  }

  try {
    expLoading.value = true;
    const params = {
      pageNumber: expCurrentPage.value,
      pageSize: expPageSize.value,
      data: {
        schemeId: selectedScheme.value.schemeId,
        expName: searchConditions.value.expName || undefined,
        account: searchConditions.value.creator || undefined,
        createTimeStart:
          searchConditions.value.createDateRange?.[0]?.format(
            'YYYY-MM-DD 00:00:00',
          ) || undefined,
        createTimeEnd:
          searchConditions.value.createDateRange?.[1]?.format(
            'YYYY-MM-DD 23:59:59',
          ) || undefined,
      },
    };

    const res = await expDesignPageExpInfo(params);
    expList.value = res?.records || [];
    expTotal.value = Number(res?.total || 0);
  } catch (error: any) {
    console.error('获取实验管理列表失败', error);
    message.error('获取实验管理列表失败');
  } finally {
    expLoading.value = false;
  }
};

// 树节点选择
const onTreeSelect = (selectedKeys: string[], info: any) => {
  if (selectedKeys.length > 0 && info.selected) {
    const node = info.node;
    if (node.selectable && node.schemeId) {
      selectedScheme.value = {
        schemeId: node.schemeId,
        scenarioId: node.scenarioId,
        scenarioName: node.scenarioName,
        schemeName: node.schemeName,
      };
      expCurrentPage.value = 1;
      fetchExpList();
    }
  } else {
    selectedScheme.value = null;
    expList.value = [];
    expTotal.value = 0;
  }
};

// 实验列表分页处理
const handleExpTableChange = (page: number, size: number) => {
  expCurrentPage.value = page;
  expPageSize.value = size;
  fetchExpList();
};

// 实验搜索
const handleExpSearch = () => {
  expCurrentPage.value = 1;
  fetchExpList();
};

// 重置实验搜索条件
const handleExpReset = () => {
  searchConditions.value = {
    expName: '',
    createDateRange: [],
    creator: '',
  };
  expCurrentPage.value = 1;
  fetchExpList();
};

// 搜索处理
const handleSearch = () => {
  if (!searchKeyword.value) {
    // 如果搜索关键词为空，显示原始数据
    treeData.value = originalTreeData.value;
    const expandedKeysList: string[] = [];
    originalTreeData.value.forEach((scenario: any) => {
      expandedKeysList.push(scenario.key);
    });
    expandedKeys.value = expandedKeysList;
    return;
  }

  // 过滤树数据
  const keyword = searchKeyword.value.toLowerCase();
  const filteredData = originalTreeData.value
    .map((scenario: any) => {
      // 检查想定名称是否匹配
      const scenarioMatches = scenario.title.toLowerCase().includes(keyword);

      // 过滤子方案
      const filteredChildren = scenario.children.filter((scheme: any) =>
        scheme.title.toLowerCase().includes(keyword),
      );

      // 如果想定名称匹配或有匹配的子节点，保留该想定
      if (scenarioMatches || filteredChildren.length > 0) {
        return {
          ...scenario,
          children: scenarioMatches ? scenario.children : filteredChildren,
        };
      }
      return null;
    })
    .filter(Boolean);

  treeData.value = filteredData;

  // 搜索时展开所有匹配的想定节点
  const expandedKeysList: string[] = [];
  filteredData.forEach((scenario: any) => {
    expandedKeysList.push(scenario.key);
  });
  expandedKeys.value = expandedKeysList;
};

// 清空搜索
const handleClearSearch = () => {
  searchKeyword.value = '';
  handleSearch();
};

// 标签页切换
const handleTabChange = (key: string) => {
  activeTab.value = key;
  selectedKeys.value = [];
  expandedKeys.value = [];
  searchKeyword.value = '';
  selectedScheme.value = null; // 重置选中的方案
  expCurrentPage.value = 1;
  expList.value = [];
  // 重置搜索条件
  searchConditions.value = {
    expName: '',
    createDateRange: [],
    creator: '',
  };
  fetchTreeData();
};

// 自动推演相关
const distributedVisible = ref(false);
const distributedLoading = ref(false);
const deductionItem = ref(null);

const openDistributedModal = async (record: any) => {
  try {
    // 先打开modal并显示loading状态
    distributedVisible.value = true;
    distributedLoading.value = true;
    deductionItem.value = null;

    // 调用接口获取实验详细信息（包含抽样样本）
    const expFullInfo = await expDesignGetExpFullInfo(record.id);

    // 将完整信息传递给自动推演组件
    deductionItem.value = {
      ...record,
      expFullInfo,
    };
  } catch (error: any) {
    console.error('获取实验详细信息失败:', error);
    message.error('获取实验详细信息失败');
    distributedVisible.value = false;
  } finally {
    distributedLoading.value = false;
  }
};

const closeDistributedModal = () => {
  distributedVisible.value = false;
  distributedLoading.value = false;
  deductionItem.value = null;
};
provide('closeDistributedModal', closeDistributedModal);

// 页面加载
onMounted(() => {
  fetchTreeData();
});
</script>

<template>
  <Page auto-content-height title="实验推演">
    <div class="flex size-full">
      <!-- 左侧想定-方案树 -->
      <div class="w-[350px] pr-4">
        <!-- 标签页切换 -->
        <a-tabs
          v-model:active-key="activeTab"
          size="small"
          @change="handleTabChange"
        >
          <a-tab-pane key="1" tab="单装想定" />
          <a-tab-pane key="2" tab="作战单元/战术行动想定" />
        </a-tabs>

        <!-- 搜索框 -->
        <div class="mb-4">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="请输入想定名称或方案名称"
            allow-clear
            @search="handleSearch"
            @clear="handleClearSearch"
          />
        </div>

        <!-- 想定-方案树容器 -->
        <div class="tree-container">
          <a-tree
            v-if="treeData && treeData.length > 0"
            v-model:selected-keys="selectedKeys"
            v-model:expanded-keys="expandedKeys"
            :tree-data="treeData"
            :loading="treeLoading"
            show-line
            @select="onTreeSelect"
          />

          <!-- 空状态 -->
          <div v-else-if="!treeLoading" class="py-8 text-center text-gray-500">
            <a-empty description="暂无数据" />
          </div>
        </div>
      </div>

      <!-- 右侧实验管理列表 -->
      <div class="w-[calc(100%-350px)]">
        <div class="experiment-management-header">
          <!-- 搜索条件 -->
          <div class="mb-4 rounded p-4">
            <a-form layout="vertical" :model="searchConditions">
              <a-row :gutter="[16, 16]" align="bottom">
                <a-col :span="7">
                  <a-form-item label="实验名称" class="mb-0">
                    <a-input
                      v-model:value="searchConditions.expName"
                      placeholder="请输入实验名称"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="创建日期" class="mb-0">
                    <a-range-picker
                      v-model:value="searchConditions.createDateRange"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="7">
                  <a-form-item label="创建人" class="mb-0">
                    <a-input
                      v-model:value="searchConditions.creator"
                      placeholder="请输入创建人"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="3" class="text-right">
                  <a-space>
                    <a-button type="primary" @click="handleExpSearch">
                      搜索
                    </a-button>
                    <a-button @click="handleExpReset"> 重置 </a-button>
                  </a-space>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </div>

        <a-table
          :columns="expColumns"
          :data-source="expList"
          :pagination="{
            current: expCurrentPage,
            pageSize: expPageSize,
            total: expTotal,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: handleExpTableChange,
            onShowSizeChange: handleExpTableChange,
          }"
          :loading="expLoading"
          row-key="expId"
          bordered
          size="small"
          :scroll="{ x: 'max-content' }"
          class="!w-full"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button
                  type="link"
                  size="small"
                  @click="openDistributedModal(record)"
                >
                  自动推演
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 自动推演弹窗 -->
    <a-modal
      destroy-on-close
      v-model:open="distributedVisible"
      :title="(deductionItem as any)?.expName || '自动推演'"
      :footer="null"
      width="85%"
    >
      <a-spin :spinning="distributedLoading" tip="正在加载实验数据...">
        <ExpDistributedDeduction
          v-if="distributedVisible && deductionItem"
          :scenario="deductionItem"
          :train-mode="activeTab === '1' ? 1 : 2"
          :train-type="3"
        />
        <div v-else-if="distributedLoading" style="height: 400px"></div>
      </a-spin>
    </a-modal>
  </Page>
</template>

<style lang="scss" scoped>
.experiment-management-header {
  h3 {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
  }
}

.tree-container {
  flex: 1;
  height: calc(100vh - 300px);
  min-height: 0;
  overflow: auto;
}

:deep(.ant-table-wrapper) {
  flex: 1;
  width: 100%;
  overflow: auto;
}

:deep(.ant-table) {
  width: 100%;
}
</style>
