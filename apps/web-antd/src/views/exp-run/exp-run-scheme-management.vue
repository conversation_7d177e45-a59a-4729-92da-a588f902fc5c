<script setup lang="ts">
import { onMounted, provide, ref } from 'vue';

// import { useRouter } from 'vue-router';
import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { scenarioPageList, schemeDesignPageSchemeInfo } from '#/api/module2';
import SchemeHumanInloop from '#/views/exp-run/components/scheme-human-inloop.vue';
import AiAutomaticDeduction from '#/views/exp-run/components/singleEquipComponent/ai-automatic-deduction.vue';

// 当前激活的标签页
const trainMode = ref(1);

// 左侧想定列表相关
const scenarioList = ref<any[]>([]);
const scenarioTotal = ref(0);
const scenarioCurrentPage = ref(1);
const scenarioPageSize = ref(10);
const scenarioLoading = ref(false);
const searchKeyword = ref('');
const selectedScenario = ref<any>(null);

// 右侧方案管理列表相关
const schemeList = ref<any[]>([]);
const schemeTotal = ref(0);
const schemeCurrentPage = ref(1);
const schemePageSize = ref(15);
const schemeLoading = ref(false);

// 搜索条件
const searchConditions = ref({
  schemeName: '',
  createDateRange: [] as any[],
  creator: '',
});

// 想定列表表格列配置
const scenarioColumns = [
  {
    title: '想定名称',
    dataIndex: 'scenarioName',
    key: 'scenarioName',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '想定描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
    align: 'center',
  },
];

// 方案管理表格列配置
const schemeColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    align: 'center',
    customRender: ({ index }: { index: number }) => index + 1,
  },
  {
    title: '名称',
    dataIndex: 'schemeName',
    key: 'schemeName',
    width: 150,
    align: 'center',
  },
  {
    title: '所属想定',
    dataIndex: 'scenarioName',
    key: 'scenarioName',
    width: 150,
    align: 'center',
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    align: 'center',
  },
  {
    title: '创建人',
    dataIndex: 'account',
    key: 'account',
    width: 100,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    align: 'center',
  },
];

// 获取想定列表
const fetchScenarioList = async () => {
  try {
    scenarioLoading.value = true;
    const params = {
      pageNumber: scenarioCurrentPage.value,
      pageSize: scenarioPageSize.value,
      data: {
        type: trainMode.value, // 1单装 2作战单元
        ...(searchKeyword.value ? { scenarioName: searchKeyword.value } : {}),
      },
    };

    const res = await scenarioPageList(params);
    scenarioList.value = res?.records || [];
    scenarioTotal.value = Number(res?.total || 0);

    // 如果没有选中的想定且有数据，自动选中第一个
    if (!selectedScenario.value && scenarioList.value.length > 0) {
      selectedScenario.value = scenarioList.value[0];
      schemeCurrentPage.value = 1;
      fetchSchemeList();
    }
  } catch (error: any) {
    console.error('获取想定列表失败', error);
    message.error('获取想定列表失败');
  } finally {
    scenarioLoading.value = false;
  }
};

// 获取方案管理列表
const fetchSchemeList = async () => {
  if (!selectedScenario.value) {
    schemeList.value = [];
    return;
  }

  try {
    schemeLoading.value = true;
    const params = {
      pageNumber: schemeCurrentPage.value,
      pageSize: schemePageSize.value,
      data: {
        scenarioId: selectedScenario.value.id,
        // 添加搜索条件
        ...(searchConditions.value.schemeName
          ? { schemeName: searchConditions.value.schemeName }
          : {}),
        ...(searchConditions.value.creator
          ? { account: searchConditions.value.creator }
          : {}),
        ...(searchConditions.value.createDateRange &&
        searchConditions.value.createDateRange.length === 2
          ? {
              createTimeStart:
                searchConditions.value.createDateRange[0]?.format(
                  'YYYY-MM-DD 00:00:00',
                ),
              createTimeEnd: searchConditions.value.createDateRange[1]?.format(
                'YYYY-MM-DD 23:59:59',
              ),
            }
          : {}),
      },
    };

    const res = await schemeDesignPageSchemeInfo(params);

    // 处理返回的数据，添加想定类型信息
    schemeList.value = (res?.records || []).map((record: any) => ({
      ...record,
      scenarioType: trainMode.value === 1 ? '单装想定' : '作战单元想定',
      scenarioName: selectedScenario.value.scenarioName,
    }));
    schemeTotal.value = Number(res?.total || 0);
  } catch (error: any) {
    console.error('获取方案管理列表失败', error);
    message.error('获取方案管理列表失败');
  } finally {
    schemeLoading.value = false;
  }
};

// 想定表格行选择
const onScenarioRowClick = (record: any) => {
  selectedScenario.value = record;
  schemeCurrentPage.value = 1;
  fetchSchemeList();
};

// 想定列表分页处理
const handleScenarioTableChange = (page: number, size: number) => {
  scenarioCurrentPage.value = page;
  scenarioPageSize.value = size;
  fetchScenarioList();
};

// 方案管理分页处理
const handleSchemeTableChange = (page: number, size: number) => {
  schemeCurrentPage.value = page;
  schemePageSize.value = size;
  fetchSchemeList();
};

// 搜索处理
const handleSearch = () => {
  scenarioCurrentPage.value = 1;
  fetchScenarioList();
};

// 清空搜索
const handleClearSearch = () => {
  searchKeyword.value = '';
  scenarioCurrentPage.value = 1;
  fetchScenarioList();
};

// 方案搜索
const handleSchemeSearch = () => {
  schemeCurrentPage.value = 1;
  fetchSchemeList();
};

// 重置方案搜索条件
const handleSchemeReset = () => {
  searchConditions.value = {
    schemeName: '',
    createDateRange: [],
    creator: '',
  };
  schemeCurrentPage.value = 1;
  fetchSchemeList();
};

// 标签页切换
const handleTabChange = (key: number) => {
  trainMode.value = key;
  selectedScenario.value = null;
  scenarioCurrentPage.value = 1;
  schemeCurrentPage.value = 1;
  schemeList.value = [];
  fetchScenarioList();
};

// AI推演相关
const aiVisible = ref(false);
const schemeInfo = ref(null);
provide('schemeInfo', schemeInfo);

const openAIModal = (record: any) => {
  schemeInfo.value = record;
  aiVisible.value = true;
};

const closeAIModal = () => {
  aiVisible.value = false;
};
provide('closeAIModal', closeAIModal);

// 人在环推演相关
const peopleVisible = ref(false);
const peopleSchemeInfo = ref(null);
provide('schemeInfo', peopleSchemeInfo);

const openPeopleModal = (record: any) => {
  peopleSchemeInfo.value = record;
  peopleVisible.value = true;
};

const closePeopleModal = () => {
  peopleVisible.value = false;
};
provide('closePeopleModal', closePeopleModal);

// 人在环推演相关 - 弹窗选择信息角色
const handlePeopleDeduction = (record: any) => {
  openPeopleModal(record);
};

// 页面加载
onMounted(() => {
  fetchScenarioList();
});
</script>

<template>
  <Page auto-content-height title="实验推演">
    <div class="flex h-full min-w-0">
      <!-- 左侧想定列表 -->
      <div class="w-1/3 pr-4">
        <!-- 标签页切换 -->
        <a-tabs
          v-model:active-key="trainMode"
          size="small"
          @change="handleTabChange"
        >
          <a-tab-pane :key="1" tab="单装想定" />
          <a-tab-pane :key="2" tab="作战单元/战术行动想定" />
        </a-tabs>

        <!-- 搜索框 -->
        <div class="mb-4">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="请输入想定名称"
            allow-clear
            @search="handleSearch"
            @clear="handleClearSearch"
          />
        </div>

        <!-- 想定列表表格 -->
        <a-table
          :columns="scenarioColumns"
          :data-source="scenarioList"
          :pagination="{
            current: scenarioCurrentPage,
            pageSize: scenarioPageSize,
            total: scenarioTotal,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: handleScenarioTableChange,
            onShowSizeChange: handleScenarioTableChange,
          }"
          :loading="scenarioLoading"
          :row-selection="{
            type: 'radio',
            selectedRowKeys: selectedScenario ? [selectedScenario.id] : [],
            onSelect: (record: any) => onScenarioRowClick(record),
          }"
          row-key="id"
          bordered
          size="small"
        />
      </div>

      <!-- 右侧方案管理列表 -->
      <div class="w-2/3">
        <div class="scheme-management-header">
          <!-- 搜索条件 -->
          <div class="mb-4 rounded p-4">
            <a-row :gutter="[16, 16]" align="bottom">
              <a-col :span="6">
                <a-form-item label="方案名称" class="mb-0">
                  <a-input
                    v-model:value="searchConditions.schemeName"
                    placeholder="请输入方案名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="创建日期" class="mb-0">
                  <a-range-picker
                    v-model:value="searchConditions.createDateRange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="创建人" class="mb-0">
                  <a-input
                    v-model:value="searchConditions.creator"
                    placeholder="请输入创建人"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4" class="text-right">
                <a-space>
                  <a-button type="primary" @click="handleSchemeSearch">
                    搜索
                  </a-button>
                  <a-button @click="handleSchemeReset"> 重置</a-button>
                </a-space>
              </a-col>
            </a-row>
          </div>
        </div>

        <a-table
          :columns="schemeColumns"
          :data-source="schemeList"
          :pagination="{
            current: schemeCurrentPage,
            pageSize: schemePageSize,
            total: schemeTotal,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: handleSchemeTableChange,
            onShowSizeChange: handleSchemeTableChange,
          }"
          :loading="schemeLoading"
          row-key="id"
          bordered
          size="small"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="openAIModal(record)">
                  AI推演
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="handlePeopleDeduction(record)"
                >
                  人在环推演
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- AI推演弹窗 -->
    <a-modal
      destroy-on-close
      v-model:open="aiVisible"
      :footer="null"
      :closable="false"
      width="50%"
    >
      <AiAutomaticDeduction
        v-if="aiVisible"
        :scheme-info="schemeInfo"
        :train-mode="trainMode"
        :train-type="1"
        @close="closeAIModal"
      />
    </a-modal>

    <!-- 人在环推演弹窗 -->
    <a-modal
      v-model:open="peopleVisible"
      title="信息角色选择"
      :footer="null"
      :closable="false"
      width="70%"
      destroy-on-close
    >
      <SchemeHumanInloop
        v-if="peopleVisible && peopleSchemeInfo"
        :scheme-info="peopleSchemeInfo"
        :scenario-name="peopleSchemeInfo?.scenarioName || ''"
        :train-mode="trainMode"
      />
    </a-modal>
  </Page>
</template>

<style lang="scss" scoped>
.scheme-management-header {
  h3 {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
  }
}

:deep(.ant-table-wrapper) {
  flex: 1;
  width: 100%;
  overflow: auto;
}

:deep(.ant-table) {
  width: 100%;
}
</style>
