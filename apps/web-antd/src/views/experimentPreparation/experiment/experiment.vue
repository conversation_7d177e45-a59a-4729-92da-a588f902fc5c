<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import experiment_right1 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right1.vue';
import experiment_right2 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right2.vue';
import experiment_right3 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right3.vue';
import experiment_right4 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right4.vue';
import experiment_right5 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right5.vue';
import experiment_right6 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right6.vue';
import experiment_right7 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right7.vue';
import experiment_right8 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right8.vue';
import experiment_right9 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right9.vue';
import experiment_right10 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right10.vue';
import experiment_right11 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right11.vue';
import experiment_right12 from '#/views/experimentPreparation/experiment/experiment_right/experiment_right12.vue';

const showRight = ref('0-0-1');

const treeData = [
  {
    title: '智能猎歼连岛上D城夺控战斗实验想定',
    key: '0',
    children: [
      {
        title: '实验想定',
        key: '0-1',
        children: [
          {
            title: '作战背景',
            key: '0-0-1',
          },
          {
            title: '作战任务',
            key: '0-0-2',
          },
          {
            title: '气象环境参数',
            key: '0-0-3',
          },
          {
            title: '作战地域',
            key: '0-0-4',
          },
          {
            title: '作战编成',
            key: '0-0-5',
          },
          {
            title: '作战编组',
            key: '0-0-6',
          },
          {
            title: '通信组网',
            key: '0-0-7',
          },
          {
            title: '阶段划分',
            key: '0-0-8',
          },
          {
            title: '作战部署',
            key: '0-0-9',
          },
          {
            title: '作战计划',
            key: '0-0-10',
          },
        ],
      },
      {
        title: '作战方案',
        key: '0-2',
        children: [
          {
            title: '智能猎歼连岛上D城夺控战斗实验方案1',
            key: '0-2-1',
            children: [
              {
                title: '实验因子',
                key: '0-2-1-1',
              },
            ],
          },
          {
            title: '智能猎歼连岛上D城夺控战斗实验方案2',
            key: '0-2-2',
            children: [
              {
                title: '实验因子',
                key: '0-2-2-1',
              },
            ],
          },
        ],
      },
      {
        title: '实验设计',
        key: '0-3',
        children: [
          {
            title: '运行设置',
            key: '0-3-1',
          },
          {
            title: '指标体系',
            key: '0-3-2',
          },
          {
            title: '数据采集',
            key: '0-3-3',
          },
        ],
      },
    ],
  },
];

const columns1 = [
  {
    title: '实验名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '实验目的',
    dataIndex: 'aim',
    key: 'aim',
  },
];
const data1 = [
  {
    name: '智能猎歼连岛上D城夺控战斗实验想定',
    aim: '“T”岛中部守军抗登陆作战失败后，“m”国先遭第2装甲旅战斗队合成兵种2营在西部地区依托有利地形，构筑阵地，组织防御，企图固守有利地形，阻滞红军进攻，为部署反攻创造条件。其担负前沿防',
  },
  {
    name: '合成营编成内坦克连攻防作战实验想定',
    aim: '202X年，红方“一带一路”战略取得实质性成果，国际影响力持续提升。Ⅱ国为维护其传统霸权地位，加大对红方围堵退制力度，企图迟滞红方现代化进程及复兴之路。蓝方“T独”势力在M国唆使下，联...',
  },
  {
    name: '新型装甲突击系统作战实验想定',
    aim: '（一）作战环境萨拉尼地区x地域；四面山地地形混杂，易守难攻；行动中可能面临风、雨、雪、霉等复杂气象条件。如，狭隧的地形限制了地面机动性和交战打击效率；间接的战斗路线增加了燃油消.',
  },
];

// 创建一个响应式变量来存储 WebSocket 实例
const socket = ref(null);
const connected = ref(false);

// 连接 WebSocket
const connect = () => {
  socket.value = new WebSocket('ws://127.0.0.1:8077/websocket');
  socket.value.addEventListener('open', () => {
    console.log('WebSocket connected');
    connected.value = true;
  });
  socket.value.onmessage = (event) => {
    console.log('Message from server:', event.data);
  };
  socket.value.onerror = (error) => {
    console.error('WebSocket error:', error);
  };
  socket.value.addEventListener('close', () => {
    console.log('WebSocket disconnected');
    connected.value = false;
  });
};

onMounted(connect);

function checkTree(key) {
  if (key === '0-0-9') {
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
      const object = {};
      object.MsgHeadID = '0x04';
      socket.value.send(JSON.stringify(object));
    } else {
      console.log('WebSocket is not connected');
    }
  } else if (key === '0-0-10') {
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
      const object = {};
      object.MsgHeadID = '0x05';
      socket.value.send(JSON.stringify(object));
    } else {
      console.log('WebSocket is not connected');
    }
  }
  showRight.value = key;
}

const open = ref(false);
const open1 = ref(false);
const experimentalManagement = () => {
  open.value = true;
};

const add = () => {
  open1.value = true;
};

const handleOk1 = (e) => {
  console.log(e);
  open1.value = false;
};

const handleOk = (e) => {
  console.log(e);
  open.value = false;
};
</script>

<template>
  <div class="body">
    <div class="experiment_left div_bor">
      <div>
        <div class="experimentalRun lb">
          <div>
            <span class="title">实验数据准备:</span>
          </div>
          <div>
            <a-button class="add-btn" @click="experimentalManagement">
              实验管理
            </a-button>
          </div>
        </div>
      </div>

      <div style="margin-top: 5px; color: white">
        选择实验:
        <a-select placeholder="请选择" style="width: 40%">
          <a-select-option value="复合装甲">复合装甲</a-select-option>
        </a-select>
        <a-button class="add-btn" @click="add">新增</a-button>
      </div>
      <div style="height: 65%">
        <a-tree
          default-expand-all
          :tree-data="treeData"
          style="margin-top: 5px"
        >
          <template #title="{ key: treeKey, title }">
            <a-dropdown :trigger="['contextmenu']" @click="checkTree(treeKey)">
              <span>{{ title }}</span>
            </a-dropdown>
          </template>
        </a-tree>
      </div>
      <div style="margin-top: auto">
        <span class="text">实验描述:</span>
        <a-textarea
          :rows="3"
          value="蓝军，以台军战车连为基础，以典型热带区域为战场，以美军作战理念及其陆军旅、营编制为参考、同时结合台军编制特点，以美军现行主流武器为基本装备所构想的“理想蓝军”。
红军，以我军坦克连为基础，火力连为支援，以我军作战理念为参考，结合特定作战概念和战法，瞄准备战打仗急需，以红军夺控要点为目标，构想其在战法行动中可能采取的战役典型行动。"
        />
        <div style="margin: 12px 0">
          <div>实验时间:</div>
          <a-input type="" value="2000/1/1 0:00" />
        </div>
        <span class="text">实验目的:</span>
        <a-textarea
          :rows="3"
          style="width: 100%"
          value="编制本概要的目的，是前瞻设计未来战争、引领军队建设战。从作战问题辨析阶段，研判主要威胁挑战，梳理作战问题清单。规范化的描述作战时空、作战对手、作战威胁、环境态势、使命任务等。"
        />
      </div>
    </div>
    <div class="experiment_right">
      <experiment_right1 v-if="showRight == '0-0-1'" type="01" />
      <experiment_right1 v-if="showRight == '0-0-2'" type="02" />
      <experiment_right2 v-if="showRight == '0-0-3'" />
      <experiment_right3 v-if="showRight == '0-0-4'" />
      <experiment_right4 v-if="showRight == '0-0-5'" type="01" />
      <experiment_right4 v-if="showRight == '0-0-6'" type="02" />
      <experiment_right5 v-if="showRight == '0-0-7'" />
      <experiment_right6 v-if="showRight == '0-0-8'" />
      <experiment_right7 v-if="showRight == '0-0-9'" />
      <experiment_right8 v-if="showRight == '0-0-10'" />
      <experiment_right9 v-if="showRight == '0-2-1-1'" />
      <experiment_right10 v-if="showRight == '0-3-1'" />
      <experiment_right11 v-if="showRight == '0-3-2'" />
      <experiment_right12 v-if="showRight == '0-3-3'" />
    </div>

    <a-modal v-model:open="open" title="实验管理" @ok="handleOk" :width="1000">
      <a-button>实验数据导入</a-button>
      <a-button>实验数据导出</a-button>
      <a-button>实验数据备份</a-button>
      <a-button>实验数据恢复</a-button>
      <a-button>修改</a-button>
      <a-button>删除</a-button>
      <a-table
        :columns="columns1"
        :data-source="data1"
        :pagination="false"
        style="margin-top: 10px"
      />
    </a-modal>

    <a-modal
      v-model:open="open1"
      title="新增实验"
      @ok="handleOk1"
      :width="1000"
    >
      <a-form name="basic" autocomplete="off">
        <a-form-item label="实验名称">
          <a-input value="xxxx实验" />
        </a-form-item>

        <a-form-item label="实验地点">
          <a-select placeholder="请选择" value="波尔图">
            <a-select-option value="波尔图">波尔图</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="实验日期">
          <a-input type="" value="2000/1/1 0:00" />
        </a-form-item>
        <a-form-item label="实验描述">
          <a-textarea value="实验任务来源描述。。。。。。。" />
        </a-form-item>
        <a-form-item label="实验目的">
          <a-textarea value="描述实验目的。。。。。。。" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped>
.lb {
  display: flex;
  justify-content: space-between;
}

.body {
  display: flex;
}

.experiment_left {
  display: flex;
  flex-direction: column;
  width: 15%;
  height: 100%;
  overflow: auto;
}

.experiment_right {
  width: 84%;
  margin-left: 1%;
}

.div_bor {
  padding: 10px;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}

</style>
