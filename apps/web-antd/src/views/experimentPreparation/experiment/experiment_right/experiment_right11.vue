<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref } from 'vue';

import * as d3 from 'd3';

const radioValue = ref('1');

const treeData = [
  {
    name: '作战能力',
    key: '0',
    id: '0',
    children: [
      {
        name: '机动能力',
        key: '01',
        id: '01',
        children: [
          {
            name: '第一转弯半径',
            key: '011',
            id: '011',
          },
          {
            name: '第二转弯半径',
            key: '012',
            id: '012',
          },
          {
            name: '爬坡度',
            key: '013',
            id: '013',
          },
          {
            name: '侧倾爬坡度',
            key: '014',
            id: '014',
          },
          {
            name: '过壕沟宽度',
            key: '015',
            id: '015',
          },
          {
            name: '涉水深度',
            key: '016',
            id: '016',
          },
          {
            name: '最大行驶距离',
            key: '017',
            id: '017',
          },
          {
            name: '油箱容量',
            key: '018',
            id: '018',
          },
          {
            name: '百公里油耗',
            key: '019',
            id: '019',
          },
          {
            name: '越野平均速度',
            key: '020',
            id: '020',
          },
        ],
      },
      {
        name: '作战能力',
        key: '02',
        id: '02',
        children: [
          {
            name: '炮管俯仰角',
            key: '0-1-1',
            id: '0-1-1',
          },
          {
            name: '机枪俯仰角',
            key: '0-1-2',
            id: '0-1-2',
          },
          {
            name: '炮弹装填时间',
            key: '0-1-3',
            id: '0-1-3',
          },
          {
            name: '炮弹射程距离',
            key: '0-1-4',
            id: '0-1-4',
          },
          {
            name: '炮弹弹坑效果',
            key: '0-1-5',
            id: '0-1-5',
          },
          {
            name: '机枪弹药装填',
            key: '0-1-6',
            id: '0-1-6',
          },
          {
            name: '机枪弹药射速',
            key: '0-1-7',
            id: '0-1-7',
          },
          {
            name: '机枪弹射程',
            key: '0-1-8',
            id: '0-1-8',
          },
        ],
      },
    ],
  },
];

const columns1 = [
  {
    title: '参数名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];
const data1 = [
  {
    name: '打击总数量',
    description: '打击总数量',
  },
  {
    name: '导致毁伤的打击数量',
    description: '导致毁伤的打击数量',
  },
];

let screenX = 180;
let screenY = 110;
let screenS = 1.2;
const treeSize = [450, 330];
let initData, link, links, node, nodeDatas, nodes;
let dga, svg;
const con = ref();
onMounted(() => {
  const width = con.value.clientWidth;
  const height = con.value.clientHeight;

  svg = d3.create('svg').attr('width', width).attr('height', height);
  dga = svg
    .append('g')
    .attr('class', 'dga')
    .attr('transform', `translate(${screenX},${screenY}) scale(${screenS})`);
  initData = treeData[0];
  update = initData;
  const dataSet = d3.hierarchy(initData);
  const tree = d3.tree().size(treeSize);
  nodes = tree(dataSet);
  links = nodes.links();
  nodeDatas = nodes.descendants();
  drawTree();
  con.value.append(svg.node());
  con.value.addEventListener('click', () => {
    deleteEl.value.style.display = 'none';
  });
});
onBeforeUnmount(() => {
  removeAll();
});
let deleteEl = ref();
let deleteDataId = 0;
function drawTree() {
  link = dga
    .selectAll('.link')
    .data(links)
    .enter()
    .append('path')
    .attr('id', (d) => {
      return `L${d.target.data.id}`;
    })
    .attr('class', 'link')
    .attr(
      'd',
      d3
        .linkHorizontal() // linkVertical() 垂直  linkHorizontal() 水平
        .x((d) => {
          return d.y;
        })
        .y((d) => {
          return d.x;
        }),
    )
    .attr('fill', 'none')
    .attr('stroke', '#fff');
  node = dga
    .selectAll('.node')
    .data(nodeDatas)
    .enter()
    .append('g')
    .attr('id', (d) => {
      return `g${d.data.id}`;
    })
    .attr('class', (d) => {
      return `node${d.children ? ' node--internal' : ' node--leaf'}`;
    })
    .attr('transform', (d) => {
      const py = 0;
      const px = 0;
      return `translate(${d.y + px},${d.x + py})`;
    })
    .on('mousedown', (d) => {
      if (d.button == 2 && countOccurrences(d.target.id, 'g') === 3) {
        deleteDataId = d.target.id;

        deleteEl.value.style.display = 'block';
        deleteEl.value.style.top = `${d.clientY}px`;
        deleteEl.value.style.left = `${d.clientX}px`;
      }
    });

  node
    .append('foreignObject')
    .attr('class', 'father')
    .attr('width', (d) => {
      return 120 - 10 * d.depth;
    })
    .attr('height', (d) => {
      return 40 - 10 * d.depth;
    })
    .attr('x', (d) => {
      return (10 * d.depth - 60) / 2 + 20;
    })
    .attr('y', (d) => {
      return (10 * d.depth - 40) / 2;
    })
    .append('xhtml:div')
    .attr('id', (d) => {
      return `gg${d.data.id}`;
    })
    .attr('class', 'df')
    .append('xhtml:div')
    .attr('id', (d) => {
      return `ggg${d.data.id}`;
    })
    .attr('class', (d) => {
      return `inputClass divClass${d.depth}`;
    })
    .attr('contenteditable', true)
    .text((d) => d.data.name);
  node.call(
    d3.drag().on('start', started).on('drag', dragging).on('end', dragend),
  );
}
let update = initData;
function deleteData() {
  const id = deleteDataId.split('ggg')[1];
  const nodeLink = [];
  const deletelinks = link.select(function (x) {
    if (x.source.data.id == id || x.target.data.id == id) {
      if (x.source.data.id == id && !nodeLink.includes(x.source.data.id)) {
        nodeLink.push(x.source.data.id);
      }
      if (!nodeLink.includes(x.target.data.id)) {
        nodeLink.push(x.target.data.id);
      }
      return this;
    }
    return null;
  });
  deletelinks.remove();
  const deletenodes = node.select(function (d) {
    return nodeLink.includes(d.data.id) ? this : null;
  });
  deletenodes.remove();
  nodeDatas = nodeDatas.filter((d) => {
    return !nodeLink.includes(d.data.id);
  });

  // links = links.filter(x =>{
  //     return !(x['source'].data.id == id || x['target'].data.id == id)
  // })
  const dd = [];
  deleteNode([update], dd);
  update = dd[0];
  deleteEl.value.style.display = 'none';
}

function updateData(newData) {
  update = newData;
  const dataSet = d3.hierarchy(update);
  const tree = d3.tree().size(treeSize);
  nodes = tree(dataSet);
  links = nodes.links();
  nodeDatas = nodes.descendants();
  drawTree();
}

function updateLink() {
  link.data(links).attr(
    'd',
    d3
      .linkHorizontal() // linkVertical() 垂直  linkHorizontal() 水平
      .x((d) => {
        return d.y;
      })
      .y((d) => {
        return d.x;
      }),
  );
}

let dragId = 1;
function started(event) {
  deleteEl.value.style.display = 'none';
  dragId = event.subject.data.id;
  event.subject.y = event.subject.y + event.dx;
  event.subject.x = event.subject.x + event.dy;
  d3.select(`#g${dragId}`).attr('transform', (d) => {
    return `translate(${event.subject.y},${event.subject.x})`;
  });
}
function dragging(event) {
  event.subject.y = event.subject.y + event.dx;
  event.subject.x = event.subject.x + event.dy;
  d3.select(`#g${dragId}`).attr('transform', () => {
    return `translate(${event.subject.y},${event.subject.x})`;
  });
  updateLink();
}
function dragend(event) {
  event.subject.fx = null;
  event.subject.fy = null;
}

function removeAll() {
  svg.selectAll('*').remove();
}

const addModel = ref();
const addNode = ref();
let addName = '';
let target = '';
function addData() {
  addModel.value.style.display = 'none';

  const targetNode = findNode(target, update);
  if (!targetNode.children) {
    targetNode.children = [];
  }
  let lastId =
    targetNode.children.length === 0
      ? 0
      : targetNode.children[targetNode.children.length - 1].id
          .toString()
          .split('');
  lastId = lastId == 0 ? 0 : lastId[lastId.length - 1];
  targetNode.children.push({
    name: addName,
    id: Number.parseInt(`${targetNode.id}${Number.parseInt(lastId) + 1}`),
  });
  removeAll();
  setTimeout(() => {
    dga = svg
      .append('g')
      .attr('class', 'dga')
      .attr('transform', `translate(${screenX},${screenY}) scale(${screenS})`);
    updateData(update);
  }, 200);
}

function dragoverEL(event) {
  event.preventDefault();
}

function drop(event) {
  event.preventDefault();
  if (countOccurrences(event.target.id, 'g') === 3) {
    target = event.target.id.split('ggg')[1];
    addModel.value.style.display = 'flex';
  }
}

const sourceDrag = ref();
function dragstart(event) {
  addName = event.target.innerHTML;
}

let clickXY = [];
let move = false;
let oldS = 1;
function onwheel(e) {
  if (screenS + e.wheelDelta / 1200 > 0.15) {
    deleteEl.value.style.display = 'none';
    const newScale = screenS + e.wheelDelta / 1200;
    const p = newScale / oldS;
    screenX = e.clientX - (e.clientX - screenX) * p;
    screenY = e.clientY - (e.clientY - screenY) * p;
    dga.attr('transform', `translate(${screenX},${screenY}) scale(${screenS})`);
    screenS = newScale;
    oldS = screenS;
    dga.attr('transform', `translate(${screenX},${screenY}) scale(${screenS})`);
  }
}
function onmousedown(e) {
  if (e.button == 1) {
    move = true;
    clickXY = [e.clientX, e.clientY];
  }
}

function onmousemove(e) {
  if (move) {
    deleteEl.value.style.display = 'none';
    screenX -= clickXY[0] - e.clientX;
    screenY -= clickXY[1] - e.clientY;
    clickXY = [e.clientX, e.clientY];
    dga.attr('transform', `translate(${screenX},${screenY}) scale(${screenS})`);
  }
}
function onmouseup(e) {
  if (e.button == 1) {
    move = false;
  }
}

function deleteNode(node, model) {
  node.forEach((re) => {
    nodeDatas.forEach((da) => {
      if (da.data.id == re.id) {
        model.push({ name: da.data.name, id: da.data.id });
      }
    });
    if (re.children && model.length > 0) {
      model[model.length - 1].children = [];
      deleteNode(re.children, model[model.length - 1].children);
    }
  });
}
function findNode(id, node) {
  if (id == node.id) {
    return node;
  }
  if (node.children) {
    for (let i = 0; i < node.children.length; i++) {
      const result = findNode(id, node.children[i]);
      if (result) {
        return result;
      }
    }
  }
  return null;
}

function countOccurrences(str, subStr) {
  return (str.match(new RegExp(subStr, 'g')) || []).length;
}
// Append the SVG element.

window.document.addEventListener('contextmenu', () => {
  // 阻止默认菜单弹出
  return false;
});
</script>

<template>
  <div class="body">
    <div class="experiment_right11_left div_bor">
      <span class="title">指挥体系树</span>
      <a-tree default-expand-all :tree-data="treeData" style="margin-top: 5px">
        <template #title="{ name }">
          <a-dropdown :trigger="['contextmenu']">
            <span ref="sourceDrag" draggable="true" @dragstart="dragstart">{{
              name
            }}</span>
          </a-dropdown>
        </template>
      </a-tree>
    </div>
    <div class="experiment_right11_center div_bor">
      <a-tabs v-model:active-key="activeKey">
        <a-tab-pane key="1" tab="作战能力指标">
          <div
            ref="con"
            id="container"
            style="width: 100%; height: 75vh"
            @dragover="dragoverEL"
            @drop="drop"
            @wheel="onwheel"
            @mouseup="onmouseup"
            @mousedown="onmousedown"
            @mousemove="onmousemove"
          >
            <!-- <img src="/static/作战能力评估体系.png" style="height: 600px"> -->
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="作战方案指标">
          <div style="text-align: center">
            <img src="/static/作战方案评估体系.png" style="width: 100%" />
          </div>
        </a-tab-pane>
      </a-tabs>
      <!--      <span>评估指标</span>-->
      <!--      <img src="/static/th.jpg" style="width: 100%">-->
    </div>
    <div class="experiment_right11_right div_bor">
      <div class="lb">
        <div>
          <span class="title">指标信息</span>
        </div>
        <div>
          <a-button class="add-btn">保存</a-button>
        </div>
      </div>
      <div style="margin-top: 15px"></div>
      <a-form-item label="指标名称">
        <a-input value="火力毁伤精度" />
      </a-form-item>
      <a-form-item label="父节点名称">
        <a-input value="火力打击能力" style="width: 150px" />
      </a-form-item>
      <a-form-item label="指标描述">
        <a-input value="火力缀伤精度" />
      </a-form-item>
      <a-form-item label="权重值">
        <a-input value="0.10" type="number" />
      </a-form-item>
      <a-form-item label="评估方法">
        <a-select placeholder="请选择" style="width: 50%" value="层次分析ANP">
          <a-select-option value="层次分析ANP">层次分析ANP</a-select-option>
        </a-select>
      </a-form-item>
      <span class="title">参数:</span>
      <a-table :columns="columns1" :data-source="data1" :pagination="false" />

      <a-form-item label="计算权重">
        <a-radio-group v-model:value="radioValue">
          <a-radio value="1">九级标度法</a-radio>
          <a-radio value="2">专家评分法</a-radio>
          <a-radio value="3">是否分工干预</a-radio>
        </a-radio-group>
      </a-form-item>
    </div>
    <div ref="deleteEl" class="model" @click="deleteData">删除</div>
    <div id="modelAdd" ref="addModel" style="display: none">
      <div class="add" style="position: relative">
        <div style="position: absolute; top: 10px; left: 10px; color: #000">
          提示
        </div>
        <div style="color: #000">是否添加该数据？</div>
        <div
          style="
            display: flex;
            justify-content: space-around;
            width: 150px;
            margin-top: 15px;
          "
        >
          <button
            ref="addNode"
            @click="addData"
            style="width: 50px; color: #000"
          >
            确认
          </button>
          <button
            ref="addNode"
            @click="addModel.style.display = 'none'"
            style="width: 50px; color: #000"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.lb {
  display: flex;
  justify-content: space-between;
}

.experiment_right11_left {
  width: 15%;
}

.experiment_right11_center {
  width: 63%;
  margin-left: 1%;
}

.experiment_right11_right {
  width: 20%;
  margin-left: 1%;
}

.div_bor {
  padding: 10px;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}

</style>
<style>

.inputClass:focus-visible {
  outline: none !important;
}

.father {
  overflow: visible;
}

.model {
  position: absolute;
  top: 0;
  left: 0;
  display: none;
  width: 48px;
  height: 20px;
  font-size: 14px;
  color: #000;
  text-align: center;
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #000;
  border-radius: 8px;
}

.model:hover {
  background-color: rgb(177 233 211);
}

.kuang {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 290px;
  border: 1px solid #000;
}

.kuang div {
  cursor: pointer;
}

.kuang div:hover {
  background-color: #ccc9;
}

#modelAdd {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background-color: #0009;
}

.add {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 250px;
  height: 150px;
  background-color: #fff;
}
</style>
