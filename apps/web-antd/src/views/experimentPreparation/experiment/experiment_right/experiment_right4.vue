<script lang="ts" setup>
import { defineProps, ref } from 'vue';

// 定义参数 已经选中的ITEMID
const props = defineProps({
  type: { type: String },
});

const span1 = ref('');
const span2 = ref('');

const treeData = ref([]);
const treeData1 = ref([]);

const columns1 = [
  {
    title: '装备型号',
    dataIndex: 'model',
    key: 'model',
  },
  {
    title: '装备数量',
    dataIndex: 'number',
    key: 'number',
  },
];
const data1 = [
  {
    model: '新型坦克',
    number: '1',
  },
  {
    model: '新型坦克',
    number: '2',
  },
  {
    model: '旋翼察打一体无人机',
    number: '1',
  },
  {
    model: '无人系统运载操控车',
    number: '2',
  },
];

console.log(props.type);

if (props.type === '01') {
  span1.value = '作战编制';
  span2.value = '作战编成';

  treeData.value = [
    {
      title: '红方',
      key: '0',
      children: [
        {
          title: '智能猎奸连',
          key: '01',
          children: [
            {
              title: '连部',
              key: '011',
              children: [
                {
                  title: '新型坦克1',
                  key: '0111',
                },
                {
                  title: '新型坦克2',
                  key: '0112',
                },
                {
                  title: '无人系统运载操控车',
                  key: '0113',
                },
                {
                  title: '装甲输送车',
                  key: '0114',
                },
                {
                  title: '指挥员1',
                  key: '0115',
                },
                {
                  title: '指挥员2',
                  key: '0116',
                },
                {
                  title: '作战人员1',
                  key: '0117',
                },
                {
                  title: '作战人员2',
                  key: '0118',
                },
                {
                  title: '技术保障人员1',
                  key: '0119',
                },
              ],
            },
          ],
        },
        {
          title: '猎奸1排',
          key: '02',
          children: [
            {
              title: '指挥员1',
              key: '021',
            },
            {
              title: '作战人员1',
              key: '022',
            },
            {
              title: '作战人员2',
              key: '0213',
            },
            {
              title: '作战人员3',
              key: '0214',
            },
            {
              title: '技术保障人员1',
              key: '0215',
            },
            {
              title: '技术保障人员2',
              key: '0216',
            },
            {
              title: '勤务保障人员1',
              key: '01117',
            },
            {
              title: '勤务保障人员2',
              key: '01118',
            },
          ],
        },
        {
          title: '坦克1连',
          key: '03',
        },
        {
          title: '火力连',
          key: '04',
        },
        {
          title: '摩托化步兵营',
          key: '05',
        },
      ],
    },
    {
      title: '蓝方',
      key: '1',
      children: [
        {
          title: '战车连',
          key: '11',
          children: [
            {
              title: '连部',
              key: '111',
            },
            {
              title: '战车1排',
              key: '112',
            },
            {
              title: '战车2排',
              key: '113',
            },
            {
              title: '战车3排',
              key: '114',
            },
          ],
        },
        {
          title: '加强排',
          key: '12',
          children: [
            {
              title: '排长',
              key: '121',
            },
            {
              title: '无人机组',
              key: '122',
            },
            {
              title: '步兵一班',
              key: '123',
            },
            {
              title: '步兵二班',
              key: '124',
            },
            {
              title: '补兵三班',
              key: '125',
            },
          ],
        },
      ],
    },
  ];

  treeData1.value = [
    {
      title: '红方',
      key: '0',
      children: [
        {
          title: '建制力量',
          key: '01',
          children: [
            {
              title: '智能猎奸连',
              key: '011',
              children: [
                {
                  title: '连部',
                  key: '01111',
                  children: [
                    {
                      title: '新型坦克1',
                      key: '01111',
                    },
                    {
                      title: '新型坦克2',
                      key: '01112',
                    },
                    {
                      title: '无人系统运载操控车',
                      key: '01113',
                    },
                    {
                      title: '装甲输送车',
                      key: '01114',
                    },
                    {
                      title: '指挥员1',
                      key: '01115',
                    },
                    {
                      title: '指挥员2',
                      key: '01131',
                    },
                    {
                      title: '作战人员1',
                      key: '0113',
                    },
                    {
                      title: '作战人员2',
                      key: '0114',
                    },
                    {
                      title: '技术保障人员1',
                      key: '0115',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          title: '猎奸1排',
          key: '02',
          children: [
            {
              title: '指挥员1',
              key: '021',
            },
            {
              title: '作战人员1',
              key: '022',
            },
            {
              title: '作战人员2',
              key: '0213',
            },
            {
              title: '作战人员3',
              key: '0214',
            },
            {
              title: '技术保障人员1',
              key: '0215',
            },
            {
              title: '技术保障人员2',
              key: '0216',
            },
            {
              title: '勤务保障人员1',
              key: '01117',
            },
            {
              title: '勤务保障人员2',
              key: '01118',
            },
          ],
        },
        {
          title: '坦克1连',
          key: '03',
        },
        {
          title: '火力连',
          key: '04',
        },
        {
          title: '摩托化步兵营',
          key: '05',
        },
      ],
    },
    {
      title: '蓝方',
      key: '1',
      children: [
        {
          title: '战车连',
          key: '11',
          children: [
            {
              title: '连部',
              key: '111',
            },
            {
              title: '战车1排',
              key: '112',
            },
            {
              title: '战车2排',
              key: '113',
            },
            {
              title: '战车3排',
              key: '114',
            },
          ],
        },
        {
          title: '加强排',
          key: '12',
          children: [
            {
              title: '排长',
              key: '121',
            },
            {
              title: '无人机组',
              key: '122',
            },
            {
              title: '步兵一班',
              key: '123',
            },
            {
              title: '步兵二班',
              key: '124',
            },
            {
              title: '补兵三班',
              key: '125',
            },
          ],
        },
      ],
    },
  ];
} else if (props.type === '02') {
  span1.value = '作战编成';
  span2.value = '作战编组';

  treeData.value = [
    {
      title: '红方',
      key: '0',
      children: [
        {
          title: '强制力量',
          key: '01',
          children: [
            {
              title: '智能猎奸连',
              key: '011',
              children: [
                {
                  title: '连部',
                  key: '0111',
                },
                {
                  title: '猎奸1排',
                  key: '0112',
                },
                {
                  title: '猎奸2排',
                  key: '0113',
                },
                {
                  title: '猎奸3排',
                  key: '0114',
                },
                {
                  title: '猎奸4排',
                  key: '0115',
                },
              ],
            },
          ],
        },
        {
          title: '配属力量',
          key: '02',
          children: [
            {
              title: '红箭10反坦克导弹1班',
              key: '021',
            },
          ],
        },
        {
          title: '支援力量',
          key: '03',
          children: [
            {
              title: '120迫榴炮1排',
              key: '031',
            },
            {
              title: '120迫榴炮2排',
              key: '032',
            },
          ],
        },
      ],
    },
    {
      title: '蓝方',
      key: '1',
      children: [
        {
          title: '强制力量',
          key: '11',
          children: [
            {
              title: '战车连',
              key: '111',
            },
            {
              title: '战车1排',
              key: '112',
            },
            {
              title: '战车2排',
              key: '113',
            },
            {
              title: '战车3排',
              key: '114',
            },
          ],
        },
        {
          title: '配属力量',
          key: '12',
          children: [
            {
              title: '无人机组',
              key: '121',
            },
            {
              title: '步兵1班',
              key: '122',
            },
            {
              title: '步兵2班',
              key: '123',
            },
            {
              title: '步兵3班',
              key: '124',
            },
          ],
        },
      ],
    },
  ];

  treeData1.value = [
    {
      title: '红方',
      key: '0',
      children: [
        {
          title: '前沿主攻群',
          key: '01',
          children: [
            {
              title: '猎奸1排',
              key: '011',
              children: [
                {
                  title: '连部',
                  key: '01111',
                  children: [
                    {
                      title: '新型坦克1',
                      key: '011111',
                    },
                    {
                      title: '新型坦克2',
                      key: '01112',
                    },
                    {
                      title: '无人系统运载操控车',
                      key: '01113',
                    },
                    {
                      title: '装甲输送车',
                      key: '01114',
                    },
                    {
                      title: '指挥员1',
                      key: '01115',
                    },
                    {
                      title: '指挥员2',
                      key: '01131',
                    },
                    {
                      title: '作战人员1',
                      key: '0113',
                    },
                    {
                      title: '作战人员2',
                      key: '01132',
                    },
                    {
                      title: '技术保障人员1',
                      key: '01133',
                    },
                    {
                      title: '无人系统运载操控车1',
                      key: '01134',
                    },
                    {
                      title: '无人系统运载操控车2',
                      key: '01135',
                    },
                  ],
                },
              ],
            },
            {
              title: '猎奸2排',
              key: '02',
              children: [
                {
                  title: '指挥员1',
                  key: '021',
                },
                {
                  title: '作战人员1',
                  key: '022',
                },
                {
                  title: '作战人员2',
                  key: '0213',
                },
                {
                  title: '作战人员3',
                  key: '0214',
                },
                {
                  title: '技术保障人员1',
                  key: '0215',
                },
                {
                  title: '技术保障人员2',
                  key: '0216',
                },
                {
                  title: '勤务保障人员1',
                  key: '01117',
                },
                {
                  title: '勤务保障人员2',
                  key: '01118',
                },
                {
                  title: '旋翼察打一体无人机',
                  key: '01119',
                },
                {
                  title: '无人系统运载操控车1',
                  key: '01120',
                },
                {
                  title: '无人系统运载操控车2',
                  key: '01121',
                },
              ],
            },
          ],
        },

        {
          title: '坦克1连',
          key: '03',
        },
        {
          title: '火力连',
          key: '04',
        },
        {
          title: '摩托化步兵营',
          key: '05',
        },
      ],
    },
    {
      title: '蓝方',
      key: '1',
      children: [
        {
          title: '战车连',
          key: '11',
          children: [
            {
              title: '连部',
              key: '111',
            },
            {
              title: '战车1排',
              key: '112',
            },
            {
              title: '战车2排',
              key: '113',
            },
            {
              title: '战车3排',
              key: '114',
            },
          ],
        },
        {
          title: '加强排',
          key: '12',
          children: [
            {
              title: '排长',
              key: '121',
            },
            {
              title: '无人机组',
              key: '122',
            },
            {
              title: '步兵一班',
              key: '123',
            },
            {
              title: '步兵二班',
              key: '124',
            },
            {
              title: '补兵三班',
              key: '125',
            },
          ],
        },
      ],
    },
  ];
}
</script>

<template>
  <div class="body">
    <div class="experiment_right4_1 div_bor">
      <span class="title">{{ span1 }}</span>

      <a-form-item label="国别">
        <a-select placeholder="请选择">
          <a-select-option value="中国人民解放军">
            中国人民解放军
          </a-select-option>
        </a-select>
      </a-form-item>

      <div style="height: 90%">
        <a-tree
          :default-expand-all="true"
          :tree-data="treeData"
          style="margin-top: 5px"
        >
          <template #title="{ key: treeKey, title }">
            <a-dropdown :trigger="['contextmenu']">
              <span>{{ title }}</span>
            </a-dropdown>
          </template>
        </a-tree>
      </div>
    </div>

    <div class="experiment_right4_2 div_bor">
      <span class="title">{{ span2 }}</span>
      <div style="height: 95%">
        <a-tree
          :default-expand-all="true"
          :tree-data="treeData1"
          style="margin-top: 5px"
        >
          <template #title="{ key: treeKey, title }">
            <a-dropdown :trigger="['contextmenu']">
              <span>{{ title }}</span>
            </a-dropdown>
          </template>
        </a-tree>
      </div>
    </div>

    <div class="experiment_right4_3 div_bor">
      <div>
        <a-button class="add-btn">下移</a-button>
      </div>
      <div>
        <a-button class="add-btn">上移</a-button>
      </div>
      <div>
        <a-button class="add-btn">升级</a-button>
      </div>
      <div>
        <a-button class="add-btn">降级</a-button>
      </div>
      <div>
        <a-button class="add-btn">增加</a-button>
      </div>
      <div>
        <a-button class="add-btn">修改</a-button>
      </div>
      <div>
        <a-button class="add-btn">删除</a-button>
      </div>
      <div>
        <a-button class="add-btn">拷贝</a-button>
      </div>
      <div>
        <a-button class="add-btn">粘贴</a-button>
      </div>
    </div>
    <div class="experiment_right4_4 div_bor">
      <div>
        <a-form-item label="单位待字">
          <a-input value="猎奸1排" />
        </a-form-item>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="军兵种">
              <a-input value="陆军" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="单位级别">
              <a-input value="排" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="单位性质">
              <a-input value="步兵" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="节点类型">
              <a-select placeholder="请选择" value="基本节点">
                <a-select-option value="基本节点">基本节点</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="人员数目">
              <a-input value="10" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="装备数目">
              <a-input value="6" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <div>
        <div class="lb">
          <div>
            <span class="title">装备列表:</span>
          </div>
          <div>
            <a-button class="add-btn" type="primary">添加装备</a-button>
            <a-button
              class="delete-btn"
              type="primary"
              danger
              style="margin-left: 10px"
            >
              删除装备
            </a-button>
          </div>
        </div>
        <a-table :columns="columns1" :data-source="data1" :pagination="false" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.lb {
  display: flex;
  justify-content: space-between;
}

.title {
  font-size: 16px;
}

.experiment_right4_3 div {
  height: 11%;
  text-align: center;
}

.div_bor {
  padding: 10px;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}

.experiment_right4_1 {
  width: 25%;
}

.experiment_right4_2 {
  width: 24%;
  margin-left: 1%;
}

.experiment_right4_3 {
  width: 6%;
  margin-left: 1%;
}

.experiment_right4_4 {
  width: 42%;
  margin-left: 1%;
}
</style>
