<script lang="ts" setup>
import { ref } from 'vue';

import right1 from '#/views/experimentPreparation/experimentalFactor/right/right1.vue';
import right2 from '#/views/experimentPreparation/experimentalFactor/right/right2.vue';
import right3 from '#/views/experimentPreparation/experimentalFactor/right/right3.vue';

const showRight = ref('0-0-1');

const treeData = [
  {
    title: '环境因子',
    key: '0-0',
    children: [
      {
        title: '气象环境因子',
        key: '0-0-1',
      },
      {
        title: '地形环境因子',
        key: '0-0-2',
      },
      {
        title: '地理环境因子',
        key: '0-0-3',
      },
    ],
  },
  {
    title: '静态装备因子',
    key: '0-1',
    children: [
      {
        title: '装备分类类别',
        key: '0-1-1',
      },
      {
        title: '分队类别',
        key: '0-1-2',
      },
      {
        title: '作战任务',
        key: '0-1-3',
      },
      {
        title: '战术行动',
        key: '0-1-4',
      },
      {
        title: '装备参数',
        key: '0-1-5',
      },
      {
        title: '作战能力',
        key: '0-1-6',
      },
    ],
  },
];

function checkTree(key) {
  showRight.value = key;
}
</script>

<template>
  <div class="body">
    <div class="left div_bor">
      <div style="text-align: center">
        <span class="title">因子分类</span>
      </div>

      <a-tree
        :default-expand-all="true"
        :tree-data="treeData"
        style="margin-top: 5px"
      >
        <template #title="{ key: treeKey, title }">
          <a-dropdown :trigger="['contextmenu']" @click="checkTree(treeKey)">
            <span>{{ title }}</span>
          </a-dropdown>
        </template>
      </a-tree>
    </div>

    <div class="right">
      <right1 v-if="showRight == '0-0-1'" />
      <right2 v-if="showRight == '0-0-2'" />
      <right3 v-if="showRight == '0-0-3'" />
    </div>
  </div>
</template>

<style scoped>
.title {
  font-size: 18px;
}

.rightTop {
  display: flex;
  height: 45%;
  margin-top: 1%;
}

.rightTopLeft {
  width: 50%;
}

.rightTopRight {
  width: 49%;
  margin-left: 1%;
}

.rightBottom {
  display: flex;
  height: 44%;
  margin-top: 1%;
}

.rightBottom1 {
  width: 33%;
}

.rightBottom2 {
  width: 32%;
  margin-left: 1%;
}

.rightBottom3 {
  width: 32%;
  margin-left: 1%;
}

.title {
  font-size: 18px;
}

.div_bor {
  padding: 10px;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}

.body {
  display: flex;
  height: 100%;
}

::v-deep .left {
  width: 10%;
}

.right {
  width: 89%;
  margin-left: 1%;
}

::v-deep .ant-table-thead > tr > th {
  padding: 5px;
}

::v-deep .ant-table-tbody > tr > td {
  padding: 5px;
}

</style>
