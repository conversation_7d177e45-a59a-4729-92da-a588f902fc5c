<script lang="ts" setup>
import { ref } from 'vue';

const radioValue = ref('1');
const radioValue2 = ref('1');
const radioValue3 = ref('1');

const columns1 = [
  {
    title: '因子名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '备注',
    dataIndex: 'remake',
    key: 'remake',
  },
];
const data1 = [
  {
    name: '森林',
    remake: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
  },
  {
    name: '草原',
    remake: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
  },
  {
    name: '荒漠',
    remake: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
  },
  {
    name: '草甸',
    remake: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
  },
  {
    name: '沼泽',
    remake: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
  },
  {
    name: '灌木丛',
    remake: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
  },
];

const columns2 = [
  {
    title: '因子名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '备注',
    dataIndex: 'remake',
    key: 'name',
  },
];

const data2 = [
  {
    name: '公路',
    remake: '',
  },
  {
    name: '水泥陆',
    remake: '',
  },
  {
    name: '铁路',
    remake: '',
  },
  {
    name: '土路',
    remake: '',
  },
];
const columns3 = [
  {
    title: '因子名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '最大值',
    dataIndex: 'maximumValue',
    key: 'maximumValue',
  },
  {
    title: '最小值',
    dataIndex: 'minimumValue',
    key: 'minimumValue',
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
  },
  {
    title: '备注',
    dataIndex: 'remake',
    key: 'remake',
  },
];
const data3 = [
  {
    name: '水宽',
    maximumValue: '',
    minimumValue: '',
    unit: '',
    remake: '横向宽度',
  },
  {
    name: '水深',
    maximumValue: '',
    minimumValue: '',
    unit: '',
    remake: '水面至水底的垂直距离：（影响车辆允许涉水…',
  },
  {
    name: '流速',
    maximumValue: '0',
    minimumValue: '4',
    unit: 'm/s',
    remake: '单位时间(s)内水体的流动距离(m)',
  },
  {
    name: '底质',
    maximumValue: '',
    minimumValue: '',
    unit: '',
    remake: '水底表层物质的性质',
  },
  {
    name: '岸质',
    maximumValue: '',
    minimumValue: '',
    unit: '',
    remake: '河岸的高度、坡度和土质的坚硬程度',
  },
  {
    name: '水质',
    maximumValue: '',
    minimumValue: '',
    unit: '',
    remake: '水体的性质，按矿物质分类：硬水、软水；按…',
  },
];
const columns4 = [
  {
    title: '强度',
    dataIndex: 'intensity',
    key: 'intensity',
  },
  {
    title: '俯仰角',
    dataIndex: 'pitchAngle',
    key: 'pitchAngle',
  },
  {
    title: '郁闭度',
    dataIndex: 'canopyDensity',
    key: 'canopyDensity',
  },
  {
    title: '俯仰角',
    dataIndex: 'pitchAngle1',
    key: 'pitchAngle1',
  },
  {
    title: '郁闭度',
    dataIndex: 'canopyDensity1',
    key: 'canopyDensity1',
  },
  {
    title: '俯仰角',
    dataIndex: 'pitchAngle2',
    key: 'pitchAngle2',
  },
  {
    title: '郁闭度',
    dataIndex: 'canopyDensity2',
    key: 'canopyDensity2',
  },
];
const data4 = [
  {
    intensity: '零星树木',
    pitchAngle: '10',
    canopyDensity: '0.04',
    pitchAngle1: '15',
    canopyDensity1: '0.02',
    pitchAngle2: '20',
    canopyDensity2: '0.01',
  },
  {
    intensity: '疏林',
    pitchAngle: '10',
    canopyDensity: '0.15',
    pitchAngle1: '15',
    canopyDensity1: '0.1',
    pitchAngle2: '20',
    canopyDensity2: '0.08',
  },
  {
    intensity: '森林',
    pitchAngle: '10',
    canopyDensity: '0.93',
    pitchAngle1: '15',
    canopyDensity1: '1.65',
    pitchAngle2: '20',
    canopyDensity2: '0.51',
  },
];

const columns5 = [
  {
    title: '强度',
    dataIndex: 'intensity',
    key: 'intensity',
  },
  {
    title: '最小值(cm)',
    dataIndex: 'minimumValue',
    key: 'maximumValue',
  },
  {
    title: '最大值(cm)',
    dataIndex: 'maximumValue',
    key: 'maximumValue',
  },
  {
    title: '备注',
    dataIndex: 'remake',
    key: 'remake',
  },
];
const data5 = [
  {
    intensity: '小径',
    minimumValue: '0',
    maximumValue: '10',
    remake: '直径小于10厘米的树木，这类数目通常处于生长初期。材质娇嫩',
  },
  {
    intensity: '中径',
    minimumValue: '10',
    maximumValue: '30',
    remake: '直径在10厘米至30厘米的树木。树木生长旺盛，材质较好。',
  },
  {
    intensity: '大径',
    minimumValue: '30',
    maximumValue: '+++',
    remake: '直径超过30厘米的树木。生长周期长，材质坚硬',
  },
];

const columns6 = [
  {
    title: '强度',
    dataIndex: 'intensity',
    key: 'intensity',
  },
  {
    title: '最小值(cm)',
    dataIndex: 'minimumValue',
    key: 'maximumValue',
  },
  {
    title: '最大值(cm)',
    dataIndex: 'maximumValue',
    key: 'maximumValue',
  },
  {
    title: '备注',
    dataIndex: 'remake',
    key: 'remake',
  },
];

const data6 = [
  {
    intensity: '淤泥',
    minimumValue: '0.1',
    maximumValue: '0.2',
    remake: '',
  },
  {
    intensity: '细沙',
    minimumValue: '0.2',
    maximumValue: '0.4',
    remake: '',
  },
  {
    intensity: '粗砂',
    minimumValue: '0.4',
    maximumValue: '0.6',
    remake: '',
  },
  {
    intensity: '砾石',
    minimumValue: '0.6',
    maximumValue: '1.2',
    remake: '',
  },
];
</script>

<template>
  <div class="body">
    <div class="right3_left">
      <a-button type="primary" style="margin-left: 10px">新增</a-button>
      <a-button type="primary" style="margin-left: 10px">删除</a-button>
      <a-button type="primary" style="margin-left: 10px">保存</a-button>
      <div class="div_bor" style="margin-top: 10px">
        <span class="title">植被种类划分</span>
        <a-table :columns="columns1" :data-source="data1" :pagination="false" />
      </div>
      <div class="div_bor" style="margin-top: 10px">
        <span class="title">道路划分</span>
        <a-table :columns="columns2" :data-source="data2" :pagination="false" />
      </div>
      <div class="div_bor" style="margin-top: 10px">
        <span class="title">水系参数</span>
        <a-table :columns="columns3" :data-source="data3" :pagination="false" />
      </div>
    </div>
    <div class="right3_right">
      <a-button type="primary" style="margin-left: 10px">因子水平拆分</a-button>
      <a-button type="primary" style="margin-left: 10px">重置</a-button>
      <a-button type="primary" style="margin-left: 10px">删除</a-button>
      <a-button type="primary" style="margin-left: 10px">保存</a-button>

      <div class="right3_right1 div_bor" style="margin-top: 10px">
        <span>植被密度划分(范围0~10000)</span>
        <br />
        <a-radio-group v-model:value="radioValue">
          <a-radio value="1">水平拆分</a-radio>
          <a-radio value="2">自定义</a-radio>
        </a-radio-group>
        <a-input type="number" style="width: 50%" value="3" />
        <a-table :columns="columns4" :data-source="data4" :pagination="false" />
        <span>植被密度划分(定义对象的直径，单位：m)</span>
      </div>

      <div class="right3_right2 div_bor" style="margin-top: 10px">
        <a-radio-group v-model:value="radioValue2">
          <a-radio value="1">水平拆分</a-radio>
          <a-radio value="2">自定义</a-radio>
        </a-radio-group>
        <a-input type="number" style="width: 50%" value="0" />

        <a-form-item label="最大值">
          <a-input type="number" value="99" />
        </a-form-item>

        <a-table :columns="columns5" :data-source="data5" :pagination="false" />
      </div>

      <div class="right3_right2 div_bor" style="margin-top: 10px">
        <a-radio-group v-model:value="radioValue3">
          <a-radio value="1">水平拆分</a-radio>
          <a-radio value="2">自定义</a-radio>
        </a-radio-group>
        <a-input type="number" style="width: 50%" value="5" />

        <a-form-item label="水系内容拆分划分">
          <a-input value="平均速度" />
        </a-form-item>

        <a-table :columns="columns6" :data-source="data6" :pagination="false" />
        <span>(备注:最小值包含，最大值不包含)</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.title {
  font-size: 18px;
}

.body {
  display: flex;
}

.right3_left {
  width: 49.5%;
}

.right3_left div {
  height: 30%;
}

.right3_right {
  width: 49.5%;
  margin-left: 1%;
}

.right3_right1 {
  height: 30%;
}

.right3_right2 {
  height: 30%;
}

.div_bor {
  padding: 10px;
  overflow-y: scroll;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}

</style>
