<script lang="ts" setup>
import { onMounted, ref } from 'vue';

// 创建一个响应式变量来存储 WebSocket 实例
const socket = ref(null);
const connected = ref(false);

// 连接 WebSocket
const connect = () => {
  socket.value = new WebSocket('ws://127.0.0.1:8077/websocket');
  socket.value.addEventListener('open', () => {
    console.log('WebSocket connected');
    connected.value = true;
  });
  socket.value.onmessage = (event) => {
    console.log('Message from server:', event.data);
  };
  socket.value.onerror = (error) => {
    console.error('WebSocket error:', error);
  };
  socket.value.addEventListener('close', () => {
    console.log('WebSocket disconnected');
    connected.value = false;
  });
};

// 在组件挂载时连接 WebSocket
onMounted(connect);

function experiment() {
  if (socket.value && socket.value.readyState === WebSocket.OPEN) {
    const object = {};
    object.MsgHeadID = '0x03';
    socket.value.send(JSON.stringify(object));
  } else {
    console.log('WebSocket is not connected');
  }
}

const treeData = [
  {
    title: '模型展示',
    key: '0',
    children: [
      {
        title: '作战环境模型',
        key: '01',
      },
      {
        title: '作战实体模型',
        key: '02',
        children: [
          {
            title: '人员实体模型',
            key: '021',
            children: [
              {
                title: '红方士兵-步枪手',
                key: '0211',
              },
            ],
          },
          {
            title: '装备实体模型',
            key: '022',
            children: [
              {
                title: 'QBZ95-1自动步枪',
                key: '0221',
              },
              {
                title: '双简望远镜',
                key: '0222',
              },
              {
                title: '96A坦克',
                key: '0223',
              },
              {
                title: 'm1a1主战坦克',
                key: '0224',
              },
              {
                title: 'm1a2主战坦克',
                key: '0225',
              },
            ],
          },
        ],
      },
      {
        title: '作战行动模型',
        key: '03',
        children: [
          {
            title: '人员行为模型',
            key: '031',
          },
          {
            title: '装备行为模型',
            key: '032',
          },
        ],
      },

      {
        title: '作战行动模型',
        key: '04',
        children: [
          {
            title: '人员行为模型',
            key: '041',
          },
          {
            title: '装备行为模型',
            key: '042',
          },
        ],
      },
      {
        title: '作战效果模型',
        key: '05',
        children: [
          {
            title: '扬尘效果模型',
            key: '051',
          },
          {
            title: '毁伤效果模型',
            key: '052',
          },
          {
            title: '开火效果模型',
            key: '053',
          },
          {
            title: '声音效果模型',
            key: '054',
          },
          {
            title: '烟雾效果模型',
            key: '055',
          },
          {
            title: '爆炸效果模型',
            key: '056',
          },
        ],
      },
    ],
  },
];
const columns1 = [
  {
    title: '校验内容',
    dataIndex: 'content',
    key: 'content',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];
const data1 = [
  {
    content: '第一转弯半径',
    description: '装备以一档速度行驶时内轮的转弯半径',
  },
  {
    content: '第二转弯半径',
    description: '装备以一档速度行驶时外轮的转弯半径',
  },
  {
    content: '覆带（车轮）中心间距',
    description: '装备左右两边车轮中心点之间的距离',
  },
  {
    content: '最大爬坡度',
    description: '装备以一档速度行驶时最大爬坡度',
  },
  {
    content: '最大侧倾爬坡度',
    description: '装备以一档速度行驶时爬坡时的最大侧倾爬坡度',
  },
  {
    content: '最大过垂直墙高度',
    description: '装备以一档速度行驶时过垂直墙的最大高度',
  },
  {
    content: '最大过壕沟宽度',
    description: '装备以一档速度行驶时过壕沟的最大宽度',
  },
  {
    content: '涉水深度',
    description: '装备以一档速度行驶时最大涉水深度',
  },
];

const columns2 = [
  {
    title: '校验内容',
    dataIndex: 'content',
    key: 'content',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];
const data2 = [
  {
    content: '炮管俯仰角',
    description: '装备炮管上下移动角度范围',
  },
  {
    content: '炮管水平旋转角度',
    description: '装备炮管水平旋转角度范围',
  },
  {
    content: '机枪俯仰角',
    description: '装备机枪上下移动角度范围',
  },
  {
    content: '机枪水平旋转角度',
    description: '装备机枪水平旋转角度范围',
  },
  {
    content: '烟雾弹冷却时间',
    description: '装备发射烟露弹遮蔽自身到被敌方识别的时间',
  },
  {
    content: '昼观瞄距离',
    description: '装备在昼间对目标发现、识别距离',
  },
  {
    content: '夜观瞄距离',
    description: '装备在夜间对目标发现、识别距离',
  },
];

const columns3 = [
  {
    title: '校验内容',
    dataIndex: 'content',
    key: 'content',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];
const data3 = [
  {
    content: '装甲抗穿甲能…',
    description: '利用穿甲弹对装备进行直面打击后的毁伤程度',
  },
  {
    content: '装甲抗穿甲能…',
    description: '利用穿甲弹对装备进行直面打击后的毁伤程度',
  },
  {
    content: '核生化防护(N…',
    description: '装备在一定时间内对核、生化或化学武器的防护措施',
  },
];

const columns4 = [
  {
    title: '校验内容',
    dataIndex: 'content',
    key: 'content',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];
const data4 = [
  {
    content: '昼间侦察半径',
    description: '装备在昼间利用观察镜、热成像仪、夜视仪等其他传.',
  },
  {
    content: '夜间侦察半径',
    description: '装备在夜间利用观察镜、热成像仪、夜视仪等其他传.',
  },
];

const columns5 = [
  {
    title: '校验内容',
    dataIndex: 'content',
    key: 'content',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];
const data5 = [
  {
    content: '昼间侦察半径',
    description: '装备在昼间利用观察镜、热成像仪、夜视仪等其他传.',
  },
  {
    content: '夜间侦察半径',
    description: '装备在夜间利用观察镜、热成像仪、夜视仪等其他传.',
  },
];
</script>

<template>
  <div class="body">
    <div class="left div_bor" style="width: 15%">
      <div style="text-align: center">
        <span class="title">模型列表</span>
      </div>

      <div style="height: 100%">
        <a-tree
          :tree-data="treeData"
          style="margin-top: 5px"
          default-expand-all
        >
          <template #title="{ key: treeKey, title }">
            <a-dropdown :trigger="['contextmenu']">
              <span>{{ title }}</span>
            </a-dropdown>
          </template>
        </a-tree>
      </div>
    </div>

    <div class="right">
      <div>
        <a-form-item label="装备名称">
          <a-input value="m1a1主战坦克" />
        </a-form-item>
      </div>

      <div class="lb">
        <span class="title">校验内容</span>
        <a-button
          class="add-btn"
          type="primary"
          style="font-size: 12px"
          @click="experiment"
        >
          实验
        </a-button>
      </div>

      <div class="rightTop">
        <div class="rightTopLeft div_bor">
          <span class="title">机动校验</span>
          <a-table
            :columns="columns1"
            :data-source="data1"
            :pagination="false"
          />
        </div>
        <div class="rightTopRight div_bor">
          <span class="title">火力校验</span>
          <a-table
            :columns="columns2"
            :data-source="data2"
            :pagination="false"
          />
        </div>
      </div>

      <div class="rightBottom">
        <div class="rightBottom1 div_bor">
          <span class="title">防护校验</span>
          <a-table
            :columns="columns3"
            :data-source="data3"
            :pagination="false"
          />
        </div>
        <div class="rightBottom2 div_bor">
          <span class="title">侦查校验</span>
          <a-table
            :columns="columns4"
            :data-source="data4"
            :pagination="false"
          />
        </div>
        <div class="rightBottom3 div_bor">
          <span class="title">通信校验</span>
          <a-table
            :columns="columns5"
            :data-source="data5"
            :pagination="false"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.lb {
  display: flex;
  justify-content: space-between;
}

.title {
  font-size: 18px;
}

.rightTop {
  display: flex;
  height: 45%;
  margin-top: 1%;
}

.rightTopLeft {
  width: 50%;
  overflow-y: scroll;
}

.rightTopRight {
  width: 49%;
  margin-left: 1%;
}

.rightBottom {
  display: flex;
  height: 44%;
  margin-top: 1%;
}

.rightBottom1 {
  width: 33%;
}

.rightBottom2 {
  width: 32%;
  margin-left: 1%;
}

.rightBottom3 {
  width: 32%;
  margin-left: 1%;
}

.title {
  font-size: 18px;
}

.div_bor {
  padding: 10px;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}

.body {
  display: flex;
}

::v-deep .left {
  width: 10%;
}

.right {
  width: 89%;
  margin-left: 1%;
}

::v-deep .ant-table-thead > tr > th {
  padding: 5px;
}

::v-deep .ant-table-tbody > tr > td {
  padding: 5px;
}

</style>
