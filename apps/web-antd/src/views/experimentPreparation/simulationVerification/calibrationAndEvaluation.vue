<script lang="ts" setup>
import { ref } from 'vue';

import verify1 from '#/views/experimentPreparation/simulationVerification/verify/verify1.vue';
import verify2 from '#/views/experimentPreparation/simulationVerification/verify/verify2.vue';
import verify3 from '#/views/experimentPreparation/simulationVerification/verify/verify3.vue';
import verify4 from '#/views/experimentPreparation/simulationVerification/verify/verify4.vue';

const activeKey = ref('1');

const treeData = [
  {
    title: '模型展示',
    key: '0',
    children: [
      {
        title: '作战环境模型',
        key: '01',
      },
      {
        title: '作战实体模型',
        key: '02',
        children: [
          {
            title: '人员实体模型',
            key: '021',
            children: [
              {
                title: '红方士兵-步枪手',
                key: '0211',
              },
            ],
          },
          {
            title: '装备实体模型',
            key: '022',
            children: [
              {
                title: 'QBZ95-1自动步枪',
                key: '0221',
              },
              {
                title: '双简望远镜',
                key: '0221',
              },
              {
                title: '96A坦克',
                key: '0221',
              },
              {
                title: 'm1a1主战坦克',
                key: '0221',
              },
              {
                title: 'm1a2主战坦克',
                key: '0221',
              },
            ],
          },
        ],
      },
      {
        title: '作战行动模型',
        key: '03',
        children: [
          {
            title: '人员行为模型',
            key: '031',
          },
          {
            title: '装备行为模型',
            key: '032',
          },
        ],
      },

      {
        title: '作战行动模型',
        key: '04',
        children: [
          {
            title: '人员行为模型',
            key: '041',
          },
          {
            title: '装备行为模型',
            key: '042',
          },
        ],
      },
      {
        title: '作战效果模型',
        key: '05',
        children: [
          {
            title: '扬尘效果模型',
            key: '051',
          },
          {
            title: '毁伤效果模型',
            key: '052',
          },
          {
            title: '开火效果模型',
            key: '053',
          },
          {
            title: '声音效果模型',
            key: '054',
          },
          {
            title: '烟雾效果模型',
            key: '055',
          },
          {
            title: '爆炸效果模型',
            key: '056',
          },
        ],
      },
    ],
  },
];
</script>

<template>
  <div class="body">
    <div class="left div_bor" style="width: 15%">
      <div style="text-align: center">
        <span class="title">模型列表</span>
      </div>
      <div style="height: 100%">
        <a-tree :tree-data="treeData" default-expand-all>
          <template #title="{ key: treeKey, title }">
            <a-dropdown :trigger="['contextmenu']">
              <span>{{ title }}</span>
            </a-dropdown>
          </template>
        </a-tree>
      </div>
    </div>

    <div class="right">
      <!--      <div style="overflow-y: scroll;height: 100%">-->
      <a-tabs v-model:active-key="activeKey">
        <a-tab-pane key="1" tab="逻辑校验">
          <verify1 />
        </a-tab-pane>
        <a-tab-pane key="2" tab="参数校验">
          <verify2 />
        </a-tab-pane>
        <a-tab-pane key="3" tab="算法校验">
          <verify3 />
        </a-tab-pane>
        <a-tab-pane key="4" tab="数据校验">
          <verify4 />
        </a-tab-pane>
      </a-tabs>
      <!--      </div>-->
    </div>
  </div>
</template>

<style scoped>
.lb {
  display: flex;
  justify-content: space-between;
}

.title {
  font-size: 18px;
}

.rightTop {
  display: flex;
  height: 45%;
  margin-top: 1%;
}

.rightTopLeft {
  width: 50%;
}

.rightTopRight {
  width: 49%;
  margin-left: 1%;
}

.rightBottom {
  display: flex;
  height: 44%;
  margin-top: 1%;
}

.rightBottom1 {
  width: 33%;
}

.rightBottom2 {
  width: 32%;
  margin-left: 1%;
}

.rightBottom3 {
  width: 32%;
  margin-left: 1%;
}

.title {
  font-size: 18px;
}

.div_bor {
  padding: 10px;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}

.body {
  display: flex;
}

::v-deep .left {
  width: 10%;
}

.right {
  width: 84%;
  height: 100%;
  margin-left: 1%;
  overflow-y: auto;
}

::v-deep .ant-table-thead > tr > th {
  padding: 5px;
}

::v-deep .ant-table-tbody > tr > td {
  padding: 5px;
}
</style>
