<script lang="ts" setup>
const onChange = (current) => {
  console.log(current);
};

const columns1 = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '数量',
    dataIndex: 'number',
    key: 'number',
  },
];
const data1 = [
  {
    name: '尾翼稳定脱壳穿甲弹',
    number: '40',
  },
  {
    name: '多用途破甲弹',
    number: '40',
  },
];

const columns2 = [
  {
    title: '武器名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '武器数量',
    dataIndex: 'number',
    key: 'number',
  },
];
const data2 = [
  {
    name: '12.7mm高射机枪',
    number: '1000发',
  },
  {
    name: '7.62mm机枪(2挺)',
    number: '40',
  },
];

const columns3 = [];
const data3 = [];

const columns4 = [];
const data4 = [];
</script>

<template>
  <div class="body">
    <div class="verify1_left div_bor">
      <div class="verify1_left1">
        <div style="height: 100%">
          <span class="">模型实图:</span>
          <a-carousel :after-change="onChange">
            <div style="">
              <img
                src="/static/m1a11.jpeg"
                style=" width: 100%;height: 100%; object-fit: cover"
              />
            </div>
            <div>
              <img src="/static/m1a12.jpg" style=" width: 100%;height: 100%" />
            </div>
            <div>
              <img src="/static/m1a13.jpg" style=" width: 100%;height: 100%" />
            </div>
          </a-carousel>
        </div>
      </div>

      <div class="verify1_left2">
        <span class="">建模展示:</span>
        <div style="text-align: center">
          <img src="/static/mia1_sanwei.png" />
        </div>
      </div>

      <div class="verify1_left3">
        <a-form-item label="专家评判">
          <a-input type="number" value="0.00" />
        </a-form-item>
        <span style="font-size: 12px"
          >(备注:对比实例与建模图，进行打分，满分为100分)</span
        >
        <a-form-item label="批注">
          <a-textarea placeholder="" :auto-size="{ minRows: 4, maxRows: 5 }" />
        </a-form-item>
      </div>
    </div>

    <div class="verify1_center div_bor">
      <span class="title">基本属性</span>

      <a-form layout="horizontal">
        <a-form-item label="长">
          <a-input type="number" value="7.92" />
        </a-form-item>
        <a-form-item label="宽">
          <a-input type="number" value="3.66" />
        </a-form-item>
        <a-form-item label="高">
          <a-input type="number" value="12.89" />
        </a-form-item>
        <a-form-item label="战斗全重">
          <a-input type="number" value="57" />
        </a-form-item>
        <a-form-item label="空车自重">
          <a-input type="number" value="57" />
        </a-form-item>
        <a-form-item label="油量">
          <a-input type="number" value="1907.6" />
        </a-form-item>
        <a-form-item label="发动机">
          <a-input type="" value="AGT-1500型燃气涡轮发动机" />
        </a-form-item>

        <a-form-item label="发动机功率">
          <a-input type="number" value="1103" />
        </a-form-item>

        <a-form-item label="主武器">
          <a-input value="RH120式120nm滑膛炮" />
        </a-form-item>

        <a-form-item>
          <template #label>
            <span>主武器弹药</span>
          </template>
          <a-table
            :columns="columns1"
            :data-source="data1"
            :pagination="false"
          />
        </a-form-item>
        <a-form-item>
          <template #label>
            <span>副武器和弹药</span>
          </template>
          <a-table
            :columns="columns2"
            :data-source="data2"
            :pagination="false"
          />
        </a-form-item>
        <a-form-item label="火控系统">
          <a-input type="" value="指挥仪式火控系统未配备独立的车长瞄准镜和" />
        </a-form-item>
        <a-form-item label="防护">
          <a-input type="" value="车体和炮塔采用复合装甲" />
        </a-form-item>

        <a-form-item label="席位">
          <a-textarea
            placeholder=""
            :auto-size="{ minRows: 4, maxRows: 5 }"
            value="驾驶员
车长
炮长
填装手"
          />
        </a-form-item>
      </a-form>
    </div>

    <div class="verify1_right div_bor">
      <span class="title">专家评判</span>

      <a-form layout="horizontal">
        <a-form-item>
          <input type="checkbox" />
          长
          <input class="custom" type="number" />
        </a-form-item>
        <a-form-item>
          <input type="checkbox" />
          宽
          <input class="custom" type="number" />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          高
          <input class="custom" type="number" />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          战斗全重
          <input class="custom" type="number" />
        </a-form-item>
        <a-form-item>
          <input type="checkbox" />
          空车自重
          <input class="custom" type="number" />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          油量
          <input class="custom" type="number" />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          发动机
          <input class="custom" type="" />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          发动机功率
          <input class="custom" type="number" />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          主武器
          <input class="custom" type="number" />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          主武器弹药
          <a-table
            :columns="columns3"
            :data-source="data3"
            :pagination="false"
          />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          副武器和弹药
          <a-table
            :columns="columns4"
            :data-source="data4"
            :pagination="false"
          />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          火控系统
          <input class="custom" type="" />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          防护
          <input class="custom" type="" />
        </a-form-item>

        <a-form-item>
          <input type="checkbox" />
          席位
          <a-textarea
            class="custom"
            placeholder=""
            :auto-size="{ minRows: 4, maxRows: 5 }"
          />
        </a-form-item>
      </a-form>

      <a-button
        type="primary"
        style=" width: 100%; margin-top: 10px;font-size: 12px"
      >
        保存
      </a-button>
    </div>
  </div>
</template>

<style scoped>
.title {
  font-size: 18px;
}

.verify1_left1 {
  height: 45%;
}

.verify1_left2 {
  height: 40%;
  margin-top: 1%;
}

.verify1_left3 {
  height: 9%;
  margin-top: 1%;
}

/* For demo */
:deep(.slick-slide) {
  height: 320px;
  overflow: hidden;
  line-height: 160px;
  text-align: center;
  background: #364d79;
}

:deep(.slick-slide h3) {
  color: #fff;
}

.custom {
  line-height: 1.5714285714285714;
  background-color: #fff;
  background-image: none;
  border-color: #d9d9d9;
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
}

.custom1 {
  width: 20%;
  margin-left: 1%;
  line-height: 1.5714285714285714;
  background-color: #fff;
  background-image: none;
  border-color: #d9d9d9;
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
}

.body {
  display: flex;
}

.verify1_left {
  width: 32%;
}

.verify1_center {
  width: 33%;
  margin-left: 1%;
}

.verify1_right {
  width: 32%;
  margin-left: 1%;
}

.div_bor {
  padding: 10px;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}
</style>
