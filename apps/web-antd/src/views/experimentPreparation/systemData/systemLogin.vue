<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';

import { getUserListAll, getUserLoginLog } from '#/api/system/user';

const userData = ref([]);

const state = reactive({
  indeterminate: false,
  checkAll: false,
  checkedList: [],
  usernameOptions: [],
});

// 左侧树
const onCheckAllChange = (e: any) => {
  Object.assign(state, {
    checkedList: e.target.checked
      ? userData.value.map((item: any) => item.id)
      : [],
    indeterminate: false,
  });
};
watch(
  () => state.checkedList,
  (val) => {
    state.indeterminate = val?.length > 0 && val.length < userData.value.length;
    state.checkAll = val.length === userData.value.length;
    getLoginLogData();
  },
);

// 右侧表格状态
const tableState = reactive({
  loading: false,
  dataSource: [],
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  },
  searchForm: {
    createTime: [],
  },
});

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    customRender: ({ index }) => index + 1,
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '账户',
    dataIndex: 'account',
    key: 'account',
  },
  {
    title: 'IP',
    dataIndex: 'ip',
    key: 'ip',
  },
  {
    title: '登录时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '登录结果',
    dataIndex: 'result',
    key: 'result',
  },
];

// 获取登录日志
const getLoginLogData = async () => {
  tableState.loading = true;
  try {
    let times = {};
    if (tableState.searchForm.createTime?.length === 2) {
      times = {
        start: dayjs(tableState.searchForm.createTime[0]).format(
          'YYYY-MM-DD HH:mm:00',
        ),
        end: dayjs(tableState.searchForm.createTime[1]).format(
          'YYYY-MM-DD HH:mm:00',
        ),
      };
    }

    const result = await getUserLoginLog({
      pageNumber: tableState.pagination.current,
      pageSize: tableState.pagination.pageSize,
      data: {
        ...times,
        ids: [...state.checkedList],
      },
    });

    tableState.dataSource = result.records || [];
    tableState.pagination.total = Number(result.total) || 0;
  } finally {
    tableState.loading = false;
  }
};

// 分页变化
const handleTableChange = (pagination: any) => {
  tableState.pagination.current = pagination.current;
  tableState.pagination.pageSize = pagination.pageSize;
  getLoginLogData();
};

// 搜索
const handleSearch = () => {
  tableState.pagination.current = 1;
  getLoginLogData();
};

// 重置搜索
const handleReset = () => {
  tableState.searchForm.createTime = [];
  tableState.pagination.current = 1;
  getLoginLogData();
};

onMounted(() => {
  getUserListAll().then((res) => {
    userData.value = res || [];
    state.usernameOptions = userData.value.map((item: any) => ({
      label: item.username,
      value: item.id,
    }));
  });
  getLoginLogData();
});
</script>
<template>
  <Page auto-content-height>
    <div class="flex size-full gap-4">
      <!-- 左侧用户选择 -->
      <a-card title="系统用户" class="w-[200px]" size="small">
        <div>
          <a-checkbox
            v-model:checked="state.checkAll"
            :indeterminate="state.indeterminate"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
        </div>
        <a-divider class="my-2" />
        <a-checkbox-group
          v-model:value="state.checkedList"
          :options="state.usernameOptions"
          class="flex flex-col gap-2"
        >
          <a-checkbox
            class="flex-1"
            v-for="item in state.usernameOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-checkbox>
        </a-checkbox-group>
      </a-card>

      <!-- 右侧表格 -->
      <div class="flex-1">
        <a-card>
          <!-- 搜索表单 -->
          <div class="mb-4">
            <a-form layout="inline" :model="tableState.searchForm">
              <a-form-item label="登录时间">
                <a-range-picker
                  v-model:value="tableState.searchForm.createTime"
                  show-time
                  format="YYYY-MM-DD HH:mm"
                  @change="handleSearch"
                />
              </a-form-item>
              <a-form-item>
                <a-button type="primary" @click="handleSearch">搜索</a-button>
                <a-button class="ml-2" @click="handleReset">重置</a-button>
              </a-form-item>
            </a-form>
          </div>

          <!-- 登录日志表格 -->
          <a-table
            :columns="columns"
            :data-source="tableState.dataSource"
            :loading="tableState.loading"
            :pagination="tableState.pagination"
            @change="handleTableChange"
            row-key="id"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'result'">
                <a-tag :color="record.result === '成功' ? 'success' : 'error'">
                  {{ record.result }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </div>
  </Page>
</template>

<style scoped></style>
