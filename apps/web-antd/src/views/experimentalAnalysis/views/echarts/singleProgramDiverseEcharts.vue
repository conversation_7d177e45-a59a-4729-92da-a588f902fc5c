<script lang="ts" setup>
import { onMounted, ref } from 'vue';

//  1. 引用 echarts
import * as echarts from 'echarts';

const myChart = ref();
const myChartBing = ref();
const myChartLd = ref();

// onMount 挂载后调用
// 在 dom 渲染完毕后进行初始化
onMounted(() => {
  // 基于准备好的dom，初始化echarts实例
  const myECharts = echarts.init(myChart.value);
  const myChartBings = echarts.init(myChartBing.value);
  const myChartLds = echarts.init(myChartLd.value);

  // 指定图表的配置项和数据
  const option = {
    xAxis: {
      data: ['第一次实验', '第二次实验', '第三次实验'],
      axisLabel: {
        // x轴文字的配置
        show: true,
        interval: 0, // 使x轴文字显示全,
        color: '#FFFFFF',
      },
    },
    legend: {
      textStyle: {
        color: '#ffffff',
      },
    },
    yAxis: {
      axisLabel: {
        color: '#FFFFFF',
      },
    },
    series: [
      {
        name: '打击响应时间',
        type: 'bar',
        data: [3.8, 2.5, 3.5],
        color: '#FEFD05',
      },
      {
        name: '打击精度',
        type: 'bar',
        data: [25, 30, 34.5],
        color: '#528138',
      },
      {
        name: '目标毁伤程度',
        type: 'bar',
        data: [50, 30, 40],
        color: '#FC0100',
      },
      {
        name: '弹药消耗量',
        type: 'bar',
        data: [200, 185, 233],
        color: '#01B0F1',
      },
      {
        name: '自身装备损失率',
        type: 'bar',
        data: [20, 18, 28],
        color: '#00AF50',
      },
    ],
  };
  // 使用刚指定的配置项和数据显示图表
  myECharts.setOption(option);

  const optionBing = {
    title: {},
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: '#ffffff',
      },
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 0.210_584, name: '0.210584' },
          { value: -0.146_99, name: '-0.146990' },
          { value: 0.491_854, name: '0.491854' },
          { value: -0.048_96, name: '-0.048960' },
          { value: -0.415_19, name: '-0.415190' },
          { value: 0.554_478, name: '0.554478' },
          { value: 0.524_396, name: '0.524396' },
          { value: 0.045_134, name: '0.045134' },
          { value: -0.256_46, name: '-0.256460' },
          { value: 0.005_508, name: '0.005508' },
          { value: 0.117_143, name: '0.117143' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };
  myChartBings.setOption(optionBing);

  const optionLd = {
    legend: {
      data: ['属性1', '属性2', '属性3'],
      textStyle: {
        color: '#ffffff',
      },
    },
    radar: {
      // shape: 'circle',
      indicator: [
        { name: '1', max: 20 },
        { name: '2', max: 20 },
        { name: '3', max: 20 },
        { name: '4', max: 20 },
        { name: '5', max: 20 },
        { name: '6', max: 20 },
        { name: '7', max: 20 },
        { name: '8', max: 20 },
        { name: '9', max: 20 },
        { name: '10', max: 20 },
        { name: '11', max: 20 },
        { name: '12', max: 20 },
        { name: '13', max: 20 },
        { name: '14', max: 20 },
        { name: '15', max: 20 },
        { name: '16', max: 20 },
        { name: '17', max: 20 },
        { name: '18', max: 20 },
        { name: '19', max: 20 },
        { name: '20', max: 20 },
      ],
    },
    series: [
      {
        name: 'Budget vs spending',
        type: 'radar',
        data: [
          {
            value: [
              1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6, 10, 20,
            ],
            name: '属性1',
            color: '#01B0F1',
          },
          {
            value: [
              6, 5, 4, 3, 2, 1, 6, 5, 4, 3, 2, 1, 6, 5, 4, 3, 2, 1, 20, 18,
            ],
            name: '属性2',
            color: '#FF0000',
          },
          {
            value: [
              1, 5, 2, 5, 7, 5, 2, 5, 7, 9, 2, 5, 7, 6, 2, 4, 10, 5, 5, 9,
            ],
            name: '属性3',
            color: '#01AE4E',
          },
        ],
      },
    ],
  };

  myChartLds.setOption(optionLd);
});
</script>

<template>
  <div class="statistics">
    <div class="statistics2">
      <div class="statistics21 div_bor">
        <div>
          <div
            ref="myChart"
            style="width: 100%; height: 300px; color: white"
          ></div>
        </div>
      </div>
      <div class="statistics21 div_bor">
        <div>
          <div ref="myChartBing" style="width: 100%; height: 300px"></div>
        </div>
      </div>
    </div>

    <div class="statistics2">
      <div class="statistics21 div_bor">
        <div>
          <img src="/static/dfzdyb_ht.png" style="width: 100%" />
        </div>
      </div>
      <div class="statistics21 div_bor">
        <div>
          <div
            ref="myChartLd"
            style="width: 100%; height: 300px; color: white"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

.statistics2 {
  display: flex;
  margin-top: 1%;
}

.statistics21 {
  width: 50%;
}

.statistics22 {
  width: 49%;
  margin-left: 1%;
}

.div_bor {
  padding: 10px;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}
</style>
