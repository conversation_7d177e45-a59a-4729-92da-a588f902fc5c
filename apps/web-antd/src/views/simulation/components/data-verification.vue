<script setup>
import { ref } from 'vue';

import property1 from '#/views/experimentPreparation/simulationVerification/verify/property/property1.vue';
import property2 from '#/views/experimentPreparation/simulationVerification/verify/property/property2.vue';
import property3 from '#/views/experimentPreparation/simulationVerification/verify/property/property3.vue';
import property4 from '#/views/experimentPreparation/simulationVerification/verify/property/property4.vue';
import property5 from '#/views/experimentPreparation/simulationVerification/verify/property/property5.vue';

const activeKey = ref('1');
const data = ['20240428150831', '20240430104150', '20240430105126'];
</script>

<template>
  <div class="flex">
    <div class="verify4_left">
      <div style="display: flex">
        <label>装备名称:</label>
        <input class="custom" value="M1A1主战坦克" />
      </div>
      <div
        class="div_bor"
        style=" height: 660px; margin-top: 10px;text-align: center"
      >
        <span style="font-size: 15px">校验记录</span>
        <a-list size="small" bordered :data-source="data">
          <template #renderItem="{ item }">
            <a-list-item>{{ item }}</a-list-item>
          </template>
        </a-list>
      </div>
    </div>

    <div class="verify4_right">
      <div class="div_bor">
        <div style="height: 2%">
          <span style="font-size: 18px">仿真试验数据</span>
        </div>

        <div style="display: flex; height: 8%">
          <div style="width: 50%">
            <span>仿真环境:</span>
            <br />
            <a-textarea
              class="custom"
              placeholder=""
              style="width: 100%"
              value="本次仿真环境与真实环境相似，气象条件为晴朗、无风雨、温度为25℃，气压为，海拔
高度为XX。"
            />
          </div>
          <div style="width: 49%; margin-left: 1%">
            <span>仿真环境:</span>
            <br />
            <a-textarea
              class="custom"
              placeholder=""
              style="width: 100%"
              value="X属试位物，象承件为骑期、2及风，无南、温度为25C,压为XXX,8
高度为XXX。"
            />
          </div>
        </div>

        <div style=" height: 5%;margin-top: 10px">
          <a-form-item label="校验方法选择">
            <a-select placeholder="请选择" value="专家打分法">
              <a-select-option value="专家打分法">专家打分法</a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </div>

      <div class="" style="">
        <a-tabs v-model:active-key="activeKey">
          <a-tab-pane key="1" tab="机动性能">
            <property1 />
          </a-tab-pane>
          <a-tab-pane key="2" tab="火力性能">
            <property2 />
          </a-tab-pane>
          <a-tab-pane key="3" tab="侦察与防护性能">
            <property3 />
          </a-tab-pane>
          <a-tab-pane key="4" tab="行动性能">
            <property4 />
          </a-tab-pane>
          <a-tab-pane key="5" tab="通信性能">
<!--            <property5 />-->
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>
<style scoped>
.div_bor {
  padding: 10px;
  border: 1px solid #888;
  border-radius: 10px;
  box-shadow: 2px 2px 2px #888;
}

.verify4_left {
  width: 15%;
}

.verify4_right {
  width: 84%;
  margin-left: 1%;
}

.custom {
  width: 70%;
  line-height: 1.5714285714285714;
  background-image: none;
  border-color: #d9d9d9;
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
}
</style>
