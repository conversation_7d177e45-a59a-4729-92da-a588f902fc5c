<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';
import { Decimal } from 'decimal.js';

import {
  evaluateIndexTree,
  evaluateIndexUpdateWeight,
  evaluateSchemeIndexTree,
} from '#/api/module2';
import { transDataSelectable } from '#/composables/data';
import indicatorTypesSelect from '#/views/sys2/eval-indicators/indicatorAlgorithm/components/indicatorTypesSelect.vue';

const props = defineProps({
  defaultType: {
    type: String,
    default: '',
  },
});
const route = useRoute();
const formState = ref({
  indicatorType: '1',
});

watch(
  () => props.defaultType,
  () => {
    nextTick(() => {
      if (!formState.value.indicatorType) {
        formState.value.indicatorType = props.defaultType;
      }
    });
  },
  { immediate: true },
);

const indicatorTreeData = ref([]);
const getIndicatorList = async () => {
  // 评估指标
  const ret =
    route.name === 'EvalPlanDesign'
      ? await evaluateSchemeIndexTree(formState.value.indicatorType)
      : await evaluateIndexTree(formState.value.indicatorType);
  indicatorTreeData.value = ret || [];
};
watch(
  () => formState.value.indicatorType,
  () => {
    nextTick(() => {
      getIndicatorList();
    });
  },
  {
    immediate: true,
    deep: true,
  },
);

const indicatorList = ref([]);
const onTreeSelect = (selectedKeys, { selectedNodes }) => {
  if (selectedNodes.length > 0) {
    // 深拷贝子节点并清理循环引用
    const directChildren = (selectedNodes[0].children || []).map(
      (child: any) => {
        // 只保留必要的字段，避免循环引用
        const cleanChild = {
          id: child.id,
          indexName: child.indexName || child.name,
          weight: child.weight || 0,
          isRoot: child.isRoot || 0,
          originalId: child.id, // 保存原始ID用于后续API调用
        };

        // 如果有其他必要字段，可以在这里添加
        if (child.parentId !== undefined) {
          cleanChild.parentId = child.parentId;
        }

        return cleanChild;
      },
    );

    indicatorList.value = transDataSelectable([...directChildren]);
  } else {
    indicatorList.value = []; // 如果没有节点被选中，则清空列表
  }
};

const indicatorColumns = ref([
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    customRender: ({ index }) => `${index + 1}`,
  },
  {
    title: '指标名称',
    dataIndex: 'indexName',
    key: 'indexName',
  },
  {
    title: '权重',
    dataIndex: 'weight',
    key: 'weight',
  },
]);

const autoAssignWeights = () => {
  const validIndicators = indicatorList.value.filter(
    (item) => item.isRoot === 0,
  );
  const count = validIndicators.length;

  if (count === 0) {
    message.warning('没有可分配权重的子节点');
    return;
  }

  const weightPerItem = new Decimal(1).div(count);

  // 均分后可能因四舍五入导致总和不为 1，手动调整第一个项的值
  for (const [i, item] of validIndicators.entries()) {
    item.weight =
      i === 0
        ? new Decimal(1)
            .minus(weightPerItem.times(count - 1))
            .toDP(3)
            .toNumber()
        : weightPerItem.toDP(3).toNumber();
  }

  message.success('权重已均分');
};

const saveWeights = () => {
  // 新增归一化计算
  const sum = Decimal.sum(...indicatorList.value.map((item) => item.weight));

  // 若总和为1，则直接交给后台
  if (!sum.equals(1)) {
    message.error(`权重之和必须为1，当前为${sum.toNumber()}`);
    return;
  }

  // 处理权重数据，清理循环引用并只保留必要字段
  const weightData = indicatorList.value.map((item: any) => {
    // 只提取必要的字段，避免循环引用
    const cleanItem = {
      id: item.originalId || item.id, // 使用原始ID
      indexName: item.indexName,
      weight: item.weight,
      isRoot: item.isRoot,
      // 只包含API需要的字段，避免传递包含循环引用的完整对象
    };

    // 如果有其他必要字段，可以在这里添加
    if (item.parentId !== undefined) {
      cleanItem.parentId = item.parentId;
    }

    return cleanItem;
  });

  evaluateIndexUpdateWeight(formState.value.indicatorType, weightData)
    .then(() => {
      message.success('权重保存成功');
      getIndicatorList();
    })
    .catch((error) => {
      console.error('权重保存失败:', error);
      message.error('权重保存失败');
    });
};
const indicatorTypesChange = (value: string) => {
  formState.value.indicatorType = value;
};
defineExpose({
  indicatorTypesChange,
});
</script>

<template>
  <a-form
    :model="formState"
    :label-col="{ style: { width: '130px' } }"
    v-if="route.name !== 'EvalPlanDesign'"
    class="form_customize_style"
  >
    <a-row justify="space-between">
      <indicatorTypesSelect @change="indicatorTypesChange" />
    </a-row>
  </a-form>
  <Page
    content-class="flex indicator-list-card-gather"
    class="indicator-algorithm-page"
  >
    <div class="flex w-full flex-col items-start">
      <div class="flex size-full flex-1 gap-4">
        <a-card title="指标树" size="small" class="side_customize_style">
          <div class="indicator-tree-manager-card">
            <a-tree
              v-if="indicatorTreeData.length > 0"
              :tree-data="indicatorTreeData"
              default-expand-all
              :field-names="{
                title: 'indexName',
                key: 'id',
                children: 'children',
              }"
              @select="onTreeSelect"
              class="tree_customize_style"
            />
          </div>
        </a-card>
        <a-card title="权重分配" class="main_card_customize_style flex-1">
          <template #extra>
            <a-space>
              <a-button
                type="primary"
                @click="autoAssignWeights"
                class="button_customize_style button_1"
              >
                自动权重分配
              </a-button>
              <a-button
                type="primary"
                @click="saveWeights"
                class="button_customize_style button_1"
              >
                保存权重分配
              </a-button>
            </a-space>
          </template>
          <a-table
            :pagination="false"
            :data-source="indicatorList"
            :columns="indicatorColumns"
            class="table_customize_style customize_style"
          >
            <template #bodyCell="{ record, column }">
              <template v-if="column.dataIndex === 'weight'">
                <a-input-number
                  v-model:value="record.weight"
                  :step="0.001"
                  :min="0"
                  :max="1"
                  @change="
                    (value) => {
                      if (value) {
                        record.weight = Number(value.toFixed(3));
                      } else {
                        record.weight = 0;
                      }
                    }
                  "
                />
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </div>
  </Page>
</template>

<style scoped>
.side_customize_style {
  ::v-deep .ant-card-body {
    height: calc(100% - 46px);
  }
}

.indicator-tree-container {
  height: calc(100vh - 250px);
  max-height: 600px;
  padding-right: 5px;
  overflow: auto;
}

.indicator-tree {
  width: 100%;
}

:deep(.ant-tree-node-content-wrapper) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.ant-tree-title) {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.indicator-algorithm-page {
  height: calc(100% - 60px);

  ::v-deep .p-5 {
    padding: 0;
  }
}
</style>
