// gen-antd-theme.cjs
const fs = require('node:fs');
const path = require('node:path');

const glob = require('glob');

const outDir = path.resolve(__dirname, 'src/styles/antd');
fs.mkdirSync(outDir, { recursive: true });

glob(
  'node_modules/ant-design-vue/es/components/*/style/index.js',
  (err, files) => {
    if (err) {
      console.error('❌ 扫描失败：', err);
      return;
    }

    files.forEach((js) => {
      const comp = path.basename(path.dirname(js));
      const compName = comp.charAt(0).toUpperCase() + comp.slice(1);
      const tpl = `
import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context';

const ${comp}: ThemeConfig['components'] = {
  ${compName}: {
    // colorBgContainer: '#0065ab',
    // borderRadius: 0,
    // colorTextPlaceholder: '#ccc',
  },
};

export default ${comp};
    `.trim();

      fs.writeFileSync(`${outDir}/${comp}.ts`, tpl, 'utf-8');
    });

    console.log('✅ 所有组件样式模板已生成至 src/styles/antd/');
  },
);
