# 1 作战部署新增接口

## 1.1 复制新增方案

用于处理web端调取的新增方案-复制想定信息，本接口只用于复制想定的装备、其他实体、计划命令信息，不包含新建方案具体信息。

**参数**

- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "schemedatabasecopy"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `scenarioID`: 需要复制的想定ID
    - `schemeID`: 新增的方案ID
    - `isSingle`: 是否单装 1-单装实验 2-战术行动

**示例**

```json
{
    "action": "schemedatabasecopy",
    "data": {
        "usrID": "uer112",
        "scenarioID": 35,
        "schemeID": 1,
        "isSingle": 1
    }
}
```

## 1.2 方案作战部署-单装/战术行动

用于处理web端调取的方案作战部署模块。

**参数**

- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "schemedeploy"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `scenarioID`: 方案对应的想定ID
    - `schemeID`: 启动的方案ID
    - `isSingle`: 是否单装 1-单装实验 2-战术行动
    - `mapID`: 地图ID

**示例**

```json
{
    "action": "schemedeploy",
    "data": {
        "usrID": "uer112",
        "scenarioID": 35,
        "schemeID": 1,
        "isSingle": 1,
        "mapID": "123"
    }
}
```

## 1.3 推演模块接口-方案/实验

### 1.3.1 AI推演

用于启动C端的AI推演模块。需要Web端完成存表后发送！本接口只接受方案ID，不接收实验ID。

**参数**

- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "schemeaiinference"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `id`:  `deduce_dispense_record`表ID, WEB生成
    - `schemeID`: 推演相关的想定ID
    - `trainMode`: 训练模式 1-单装实验，2-作战单元/战术行动实验
    - `recordID`: `deduce_record`表ID, WEB生成

**示例**

```json
{
    "action" : "schemeaiinference",
    "data" : {
        "id" : "1",
        "schemeID":"1",
        "trainMode": 1,
        "recordID": 1
    }
}
```

### 1.3.2 人在环推演

用于启动C端的人在环推演模块。需要Web端完成存表后发送！

**参数**

- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "schemehumaninloop"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `isTrain`: 0-用户确认信息分发(是否参加) 1-服务器启动 2-用户信息收集(是否确认参加) 3-客户端启动
    - `schemeID`: 推演的方案ID
    - `trainMode`: 训练模式 1-单装实验，2-作战单元/战术行动实验
    - `sendIP` : 发送端IP
    - `seatInfo`: 服务端需要分发的席位信息，主要两个作用(1)分发用户确认信息(2)分发客户端启动信息
      ***QT端发送接收***
    - `isAgree`: 用于反馈本机是否确认参加推演(QT端使用)
      ***启动人在环推演使用参数（包括服务器和客户端）***
    - `recordID`: `deduce_record`表ID, WEB生成, `isTrain`为1/3时需要使用
    - `id`:  `deduce_dispense_record`表ID, WEB生成，`isTrain`为1/3时需要使用

**示例**

```json
{
	"action" : "schemehumaninloop",
	"data" : {
		"id" : "1",
    "isTrain" : 0,
		"schemeID":"1",
		"trainMode": 1,
		"sendIP": "***********",
    "seatInfo" : "[[总导演席位,Deirector,************],[99A坦克,objname,***********],[99A坦克2,objname2,***********]]"
	}
}
```

**客户端同意反馈**
存储路径：qt端exe同路径下config/agreeMsg.json

```json
{
    "************": true,
    "************": false
}
```

### 1.3.3 批量推演

用于启动C端的批量推演模块。需要Web端完成存表后发送！

**参数**

- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "schemebatchinference"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `id`:  `deduce_dispense_record`表ID, WEB生成推演记录ID
    - `schemeID`: 批量推演的实验ID
    - `trainMode`: 训练模式 1-单装实验，2-作战单元/战术行动实验

**示例**

```json
{
	"action" : "schemebatchinference",
	"data" : {
		"id" : "1",
		"schemeID":"1",
		"trainMode": 1
	}
}
```

### 1.3.4 批量推演实时控制

用于实时控制批量推演模块的开始暂停以及倍速。

**参数**

- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action`:"batchcontrol"
  - `data`: 请求的具体消息内容, isStart和speed可以不同时存在
    - `usrID`: 用户ID(可选)
    - `isStart`:控制是否开始，1-开始，0-暂停
    - `speed`: 控制推演倍速-小数

```json
{
        "action":"batchcontrol",
        "data": {
                "usrID" : "usr1",
                "isStart": 0,
                "speed": 1.00000
        }
}
```
