# HttpRequestDAL 类接口文档

## 1. 新增操作接口

### 1.1 地形查看

用于处理web端右键调取的地形查看窗口

**参数**
- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "b_terrainview"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `mapClassName`: 地图名称 (必须发送唯一的地图名称,不要_cfg.pbo这类的后缀)

**示例**

```json
{
    "action": "b_terrainview",
    "data": {
        "usrID": "uer112",
        "mapClassName":"map_afghangeotypical25km"
    }
}
```

### 1.2 回放数据查看

用于调取本地回放窗口

**参数**
- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "playbackvbs"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `replayFileName`: 回放的文件夹名称(据了解路径固定)

**示例**

```json
{
    "action":  "playbackvbs",
    "data" : {
        "usrID" : "uer112",
        "replayFileName" : "2025_05_20_19_43_44.cyTest0520.map_easterneuropeangeotypical25km"
    }
}
```

### 1.3 模型仿真校验

用于调取本地模型仿真校验窗口

**参数**
- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "startsimulation"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `modelName`: 模型名称
    - `modelClass`: 模型类型

**示例**

```json
{
    "action": "startsimulation",
    "data" :{
        "usrID" : "uer112",
        "modelName":"tank1",
        "modelClass":"VBS2_AU_Army_M1A1_W_X"
    }
}
```

### 1.4 作战部署/计划录入界面

用于调取本地作战部署/计划录入窗口

**参数**
- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "battledeploy"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `scenarioId`: 想定ID
    - `scenarioName`: 想定名称，名称一定要是需要调取的想定名称，不含.pbo但需要.Sama这种地图后缀
    - `filePath`: 作战力量文件读取路径，暂时未定是读取文件还是数据库，暂时忽略

**示例**

```json
{
    "action" : "battledeploy",
    "data" : {
        "usrID" : "uer112",
        "scenarioId":"123",
        "scenarioName":"ModelTest.Sama",
        "filePath": "..../xxx.xml"
    }
}
```

### 1.5 共享文件上传

用于将文件/文件夹上传至服务器

**参数**
- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "upload"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `fileName`: 如果是文件夹则传入文件夹名称即可，文件名称一定要带.xxx
    - `filePath`: 文件原始路径的父文件夹
    - `targetPath`: 文件需要上传到的共享文件夹，文件/文件夹放在该目录下
    - `isCover`: 布尔型，文件/文件夹是否覆盖，覆盖会导致原路径文件无法复原

**示例**

```json
{
    "action" : "upload",
    "data" : {
        "usrID" : "uer112",
        "fileName":"xxx.txt",
        "filePath": " C:/PlaybackVideo",
        "targetPath": "wj_sharefile/WJProject/STSPlayBack",
        "isCover": false
    }
}
```

### 1.6 共享文件下载

用于将文件/文件夹下载至服务器

**参数**
- `requestData`: 包含请求数据的JSON对象，包含以下字段
  - `action` : "download"(固定请求名称)
  - `data` : 请求的具体消息内容
    - `usrID`: 用户ID (可选)
    - `fileName`: 如果是文件夹则传入文件夹名称即可，文件名称一定要带.xxx
    - `filePath`: 文件原始路径的父文件夹
    - `targetPath`: 文件需要上传到的共享文件夹，文件/文件夹放在该目录下
    - `isCover`: 布尔型，文件/文件夹是否覆盖，覆盖会导致原路径文件无法复原

**示例**

```json
{
    "action" : "upload",
    "data" : {
        "usrID" : "uer112",
        "fileName":"xxx.txt",
        "filePath": " wj_sharefile/WJProject/STSPlayBack",
        "targetPath": "C:/PlaybackVideo"
        "isCover": true
    }
}
```