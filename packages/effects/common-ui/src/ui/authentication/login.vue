<script setup lang="ts">
import type { Recordable } from '@vben/types';

import type { VbenFormSchema } from '@vben-core/form-ui';

import type { AuthenticationProps } from './types';

import { computed, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { $t } from '@vben/locales';

import { useVbenForm } from '@vben-core/form-ui';
import { VbenCheckbox } from '@vben-core/shadcn-ui';

import Title from './auth-title.vue';

interface Props extends AuthenticationProps {
  formSchema: VbenFormSchema[];
}

defineOptions({
  name: 'AuthenticationLogin',
});

const props = withDefaults(defineProps<Props>(), {
  codeLoginPath: '/auth/code-login',
  forgetPasswordPath: '/auth/forget-password',
  formSchema: () => [],
  loading: false,
  qrCodeLoginPath: '/auth/qrcode-login',
  registerPath: '/auth/register',
  showCodeLogin: false,
  showForgetPassword: true,
  showQrcodeLogin: false,
  showRegister: false,
  showRememberMe: true,
  showThirdPartyLogin: false,
  submitButtonText: '',
  subTitle: '',
  title: '',
});

const emit = defineEmits<{
  submit: [Recordable<any>];
}>();

const [Form, formApi] = useVbenForm(
  reactive({
    commonConfig: {
      hideLabel: true,
      hideRequiredMark: true,
    },
    schema: computed(() => props.formSchema),
    showDefaultActions: false,
  }),
);
const router = useRouter();

const REMEMBER_ME_KEY = `REMEMBER_ME_USERNAME_${location.hostname}`;

const localUsername = localStorage.getItem(REMEMBER_ME_KEY) || '';

const rememberMe = ref(!!localUsername);

async function handleSubmit() {
  const { valid } = await formApi.validate();
  const values = await formApi.getValues();
  if (valid) {
    localStorage.setItem(
      REMEMBER_ME_KEY,
      rememberMe.value ? values?.account : '',
    );
    emit('submit', values);
  }
}

function handleGo(path: string) {
  router.push(path);
}

onMounted(() => {
  if (localUsername) {
    formApi.setFieldValue('account', localUsername);
  }
});

defineExpose({
  getFormApi: () => formApi,
});
</script>

<template>
  <div @keydown.enter.prevent="handleSubmit" class="login-container">
    <slot name="title">
      <Title>
        <slot name="title">
          {{ title || `${$t('authentication.welcomeBack')}` }}
        </slot>
        <!-- <template #desc>
          <span class="text-muted-foreground">
            <slot name="subTitle">
              {{ subTitle || $t('authentication.loginSubtitle') }}
            </slot>
          </span>
        </template> -->
      </Title>
    </slot>

    <Form />

    <div
      v-if="showRememberMe || showForgetPassword"
      class="mb-6 flex justify-between"
    >
      <div class="flex-center">
        <VbenCheckbox
          v-if="showRememberMe"
          v-model:checked="rememberMe"
          name="rememberMe"
          class="!text-[#b3bfc6]"
        >
          记住账号
        </VbenCheckbox>
      </div>

      <span
        v-if="showForgetPassword"
        class="vben-link text-sm font-normal !text-[#CED8DD]"
        @click="handleGo(forgetPasswordPath)"
      >
        忘记密码
      </span>
    </div>
    <!--    <VbenButton-->
    <!--      :class="{-->
    <!--        'cursor-wait': loading,-->
    <!--      }"-->
    <!--      :loading="loading"-->
    <!--      aria-label="login"-->
    <!--      class="w-full bg-[url('/login/login-btn.png')] bg-cover bg-no-repeat !p-0"-->
    <!--      @click="handleSubmit"-->
    <!--    >-->
    <!--    </VbenButton>-->
    <div
      class="-ml-[6px] h-[54px] w-[400px] cursor-pointer bg-[url('/login/login-btn2.png')] bg-cover bg-no-repeat !p-0 hover:bg-[url('/login/login-btn-hover2.png')]"
      @click="handleSubmit"
      v-loading="loading"
    ></div>

    <!--    <div-->
    <!--      v-if="showCodeLogin || showQrcodeLogin"-->
    <!--      class="mb-2 mt-4 flex items-center justify-between"-->
    <!--    >-->
    <!--      <VbenButton-->
    <!--        v-if="showCodeLogin"-->
    <!--        class="w-1/2"-->
    <!--        variant="outline"-->
    <!--        @click="handleGo(codeLoginPath)"-->
    <!--      >-->
    <!--        {{ $t('authentication.mobileLogin') }}-->
    <!--      </VbenButton>-->
    <!--      <VbenButton-->
    <!--        v-if="showQrcodeLogin"-->
    <!--        class="ml-4 w-1/2"-->
    <!--        variant="outline"-->
    <!--        @click="handleGo(qrCodeLoginPath)"-->
    <!--      >-->
    <!--        {{ $t('authentication.qrcodeLogin') }}-->
    <!--      </VbenButton>-->
    <!--    </div>-->

    <!--    &lt;!&ndash; 第三方登录 &ndash;&gt;-->
    <!--    <slot name="third-party-login">-->
    <!--      <ThirdPartyLogin v-if="showThirdPartyLogin" />-->
    <!--    </slot>-->

    <slot name="to-register">
      <div v-if="showRegister" class="mt-3 text-center text-sm">
        {{ $t('authentication.accountTip') }}
        <span
          class="vben-link text-sm font-normal"
          @click="handleGo(registerPath)"
        >
          {{ $t('authentication.createAccount') }}
        </span>
      </div>
    </slot>
  </div>
</template>
<style lang="scss" scoped>
.login-container {
  max-width: 388px;
}
</style>
