import type {
  ApplicationConfig,
  VbenAdminProAppConfigRaw,
} from '@vben/types/global';

/**
 * 由 vite-inject-app-config 注入的全局配置
 */
export function useAppConfig(
  env: Record<string, any>,
  isProduction: boolean,
): ApplicationConfig {
  // 生产环境下，直接使用 window._VBEN_ADMIN_PRO_APP_CONF_ 全局变量
  const config = isProduction
    ? window._VBEN_ADMIN_PRO_APP_CONF_
    : (env as VbenAdminProAppConfigRaw);

  const { VITE_GLOB_API_URL, VITE_GLOB_WS_URL, VITE_GLOB_FILE_SERVER, VITE_GLOB_CLIENT_SERVER, VITE_GLOB_CURRENT_ORIGIN, VITE_GLOB_KKFILEVIEWER_URL } =
    config;

  // 如果配置文件中没有设置 currentOrigin，则自动获取
  const getCurrentOrigin = () => {
    if (VITE_GLOB_CURRENT_ORIGIN && VITE_GLOB_CURRENT_ORIGIN.trim()) {
      return VITE_GLOB_CURRENT_ORIGIN;
    }
    // 自动获取当前页面的协议、主机和端口
    const { protocol, hostname, port } = window.location;
    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;
  };

  return {
    apiURL: VITE_GLOB_API_URL,
    wsURL: VITE_GLOB_WS_URL,
    fileServer: VITE_GLOB_FILE_SERVER,
    clientServer: VITE_GLOB_CLIENT_SERVER,
    currentOrigin: getCurrentOrigin(),
    kkFileViewerUrl: VITE_GLOB_KKFILEVIEWER_URL,
  };
}
