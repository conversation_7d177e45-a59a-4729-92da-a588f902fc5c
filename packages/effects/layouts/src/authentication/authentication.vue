<script setup lang="ts">
import type { ToolbarType } from './types';

import { preferences, usePreferences } from '@vben/preferences';

import { Copyright } from '../basic/copyright';
import AuthenticationFormView from './form.vue';
import Toolbar from './toolbar.vue';

interface Props {
  appName?: string;
  logo?: string;
  pageTitle?: string;
  pageDescription?: string;
  sloganImage?: string;
  toolbar?: boolean;
  copyright?: boolean;
  toolbarList?: ToolbarType[];
}

withDefaults(defineProps<Props>(), {
  appName: '',
  copyright: true,
  logo: '',
  pageDescription: '',
  pageTitle: '',
  sloganImage: '',
  toolbar: true,
  toolbarList: () => ['color', 'language', 'layout', 'theme'],
});

const { authPanelCenter, authPanelLeft, authPanelRight, isDark } =
  usePreferences();
</script>

<template>
  <div
    :class="[isDark]"
    class="flex min-h-full flex-1 select-none overflow-x-hidden bg-[url('/login/login-bg1.png')] bg-cover bg-no-repeat"
  >
    <template v-if="toolbar">
      <slot name="toolbar">
        <Toolbar :toolbar-list="toolbarList" />
      </slot>
    </template>
    <!-- 左侧认证面板 -->
    <AuthenticationFormView
      v-if="authPanelLeft"
      class="min-h-full w-2/5 flex-1"
      transition-name="slide-left"
    >
      <template v-if="copyright" #copyright>
        <slot name="copyright">
          <Copyright
            v-if="preferences.copyright.enable"
            v-bind="preferences.copyright"
          />
        </slot>
      </template>
    </AuthenticationFormView>

    <!-- 头部 Logo 和应用名称 -->
    <div
      class="flex h-[100vh] w-[100vw] flex-col items-center justify-center gap-5"
    >
      <div
        v-if="logo || appName"
        class="flex items-center justify-center gap-10"
      >
        <div class="text-foreground flex flex-1 items-center justify-center">
          <img :alt="appName" src="/login/login-title.png" />
        </div>
      </div>
      <!-- 中心认证面板 -->
      <div v-if="authPanelCenter" class="flex-center relative w-full">
        <AuthenticationFormView
          class="AuthenticationFormView_container w-full flex-1 rounded-3xl bg-[url('/login/login-form.png')] bg-contain bg-no-repeat pb-20 xl:w-[774px]"
        >
          <template v-if="copyright" #copyright>
            <slot name="copyright">
              <Copyright
                v-if="preferences.copyright.enable"
                v-bind="preferences.copyright"
              />
            </slot>
          </template>
        </AuthenticationFormView>
      </div>
    </div>

    <!-- 右侧认证面板 -->
    <AuthenticationFormView
      v-if="authPanelRight"
      class="min-h-full w-[30%] flex-1"
    >
      <template v-if="copyright" #copyright>
        <slot name="copyright">
          <Copyright
            v-if="preferences.copyright.enable"
            v-bind="preferences.copyright"
          />
        </slot>
      </template>
    </AuthenticationFormView>
  </div>
</template>

<style scoped>
.login-background {
  background: linear-gradient(
    154deg,
    #07070915 30%,
    hsl(var(--primary) / 30%) 48%,
    #07070915 64%
  );
  filter: blur(100px);
}

.dark {
  .login-background {
    background: linear-gradient(
      154deg,
      #07070915 30%,
      hsl(var(--primary) / 20%) 48%,
      #07070915 64%
    );
    filter: blur(100px);
  }
}
.AuthenticationFormView_container {
  position: relative;
  ::v-deep .bottom-3 {
    bottom: 33px;
  }
}
</style>
