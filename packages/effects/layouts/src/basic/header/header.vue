<script lang="ts" setup>
import { computed, onMounted, ref, useSlots } from 'vue';

import { useGoMain, useRefresh } from '@vben/hooks';
import { preferences, usePreferences } from '@vben/preferences';
import { useAccessStore, useMenuTitle } from '@vben/stores';

import { VbenFullScreen } from '@vben-core/shadcn-ui';

interface Props {
  /**
   * Logo 主题
   */
  theme?: string;
}

defineOptions({
  name: 'LayoutHeader',
});

withDefaults(defineProps<Props>(), {
  theme: 'light',
});

const emit = defineEmits<{ clearPreferencesAndLogout: [] }>();

const REFERENCE_VALUE = 50;

const accessStore = useAccessStore();
const { globalSearchShortcutKey, preferencesButtonPosition } = usePreferences();
const slots = useSlots();
const { refresh } = useRefresh();
const { goMain } = useGoMain();

const rightSlots = computed(() => {
  const list = [{ index: REFERENCE_VALUE + 100, name: 'user-dropdown' }];
  if (preferences.widget.refresh) {
    list.push({
      index: REFERENCE_VALUE + 60,
      name: 'refresh',
    });
  }
  if (preferences.widget.globalSearch) {
    list.push({
      index: REFERENCE_VALUE,
      name: 'global-search',
    });
  }

  if (preferencesButtonPosition.value.header) {
    list.push({
      index: REFERENCE_VALUE + 10,
      name: 'preferences',
    });
  }
  if (preferences.widget.themeToggle) {
    list.push({
      index: REFERENCE_VALUE + 20,
      name: 'theme-toggle',
    });
  }
  if (preferences.widget.fullscreen) {
    list.push({
      index: REFERENCE_VALUE + 40,
      name: 'fullscreen',
    });
  }
  if (preferences.widget.notification) {
    list.push({
      index: REFERENCE_VALUE + 50,
      name: 'notification',
    });
  }

  Object.keys(slots).forEach((key) => {
    const name = key.split('-');
    if (key.startsWith('header-right')) {
      list.push({ index: Number(name[2]), name: key });
    }
  });
  return list.sort((a, b) => a.index - b.index);
});

const leftSlots = computed(() => {
  const list: Array<{ index: number; name: string }> = [];

  // if (preferences.widget.refresh) {
  //   list.push({
  //     index: 0,
  //     name: 'refresh',
  //   });
  // }

  Object.keys(slots).forEach((key) => {
    const name = key.split('-');
    if (key.startsWith('header-left')) {
      list.push({ index: Number(name[2]), name: key });
    }
  });
  return list.sort((a, b) => a.index - b.index);
});

function clearPreferencesAndLogout() {
  emit('clearPreferencesAndLogout');
}
const menutitle = useMenuTitle();
const title = ref('');
onMounted(() => {
  title.value = menutitle.getMenuTitle();
});
</script>

<template>
  <div class="flex h-full items-center justify-center">
    <div
      class="main_title flex h-3/5 w-full items-center rounded-md px-4 text-center text-gray-200 shadow-md hover:cursor-pointer"
    >
      <div @click="goMain" class="main_title_left">
        <img
          src="../../../../../../apps/web-antd/public/main/header_five_pointed_star.png"
        />
        <img
          src="../../../../../../apps/web-antd/public/main/header_title.png"
        />
      </div>

      <!-- <a-button type="text" @click="goMain">{{ title }}</a-button> -->
    </div>
  </div>
  <div class="flex-center hidden lg:block">
    <slot name="breadcrumb"></slot>
  </div>
  <template
    v-for="slot in leftSlots.filter((item) => item.index > REFERENCE_VALUE)"
    :key="slot.name"
  >
    <slot :name="slot.name"></slot>
  </template>
  <div
    :class="`menu-align-${preferences.header.menuAlign}`"
    class="ml-5 flex h-full min-w-0 flex-1 items-center"
  >
    <slot name="menu"></slot>
  </div>
  <div class="flex h-full min-w-0 flex-shrink-0 items-center">
    <template v-for="slot in rightSlots" :key="slot.name">
      <slot :name="slot.name">
        <template v-if="slot.name === 'refresh'">
          <!-- <VbenIconButton class="my-0 mr-1 rounded-md" @click="refresh">
            <RotateCw class="size-4" />
          </VbenIconButton> -->
        </template>
        <template v-if="slot.name === 'global-search'">
          <!-- <GlobalSearch
            :enable-shortcut-key="globalSearchShortcutKey"
            :menus="accessStore.accessMenus"
            class="mr-1 sm:mr-4"
          /> -->
        </template>
        <template v-else-if="slot.name === 'preferences'">
          <!-- <PreferencesButton
            class="mr-1"
            @clear-preferences-and-logout="clearPreferencesAndLogout"
          /> -->
        </template>
        <template v-else-if="slot.name === 'theme-toggle'">
          <!-- <ThemeToggle class="mr-1 mt-[2px]" /> -->
        </template>
        <template v-else-if="slot.name === 'language-toggle'">
          <!-- <LanguageToggle class="mr-1" /> -->
        </template>
        <template v-else-if="slot.name === 'fullscreen'">
          <VbenFullScreen class="mr-1" />
        </template>
      </slot>
    </template>
  </div>
</template>
<style lang="scss" scoped>
.menu-align-start {
  --menu-align: start;
}

.menu-align-center {
  --menu-align: center;
}

.menu-align-end {
  --menu-align: end;
}

.main_title {
  padding-left: 0;
  box-shadow: none;

  button {
    height: auto;
    font-size: 24px;
    color: #fff;
  }
}
.main_title_left {
  display: flex;
  align-items: center;
  img:nth-child(1) {
    width: 50px;
  }
  img:nth-child(2) {
    width: 500px;
    margin-left: 10px;
  }
}
</style>
