import { defineStore } from 'pinia';

interface AppState {
  menuTitle: string;
}

export const useMenuTitle = defineStore('menu-title', {
  actions: {
    getMenuTitle() {
      return localStorage.getItem('menu-title');
    },
    setMenuTitle(title: string) {
      localStorage.setItem('menu-title', title);
      this.menuTitle = title;
    },
  },
  state: (): AppState => ({
    menuTitle: '',
  }),
});
