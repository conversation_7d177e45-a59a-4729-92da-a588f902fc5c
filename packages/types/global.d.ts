import type { RouteMeta as IRouteM<PERSON> } from '@vben-core/typings';

import 'vue-router';

declare module 'vue-router' {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  interface RouteMeta extends IRouteMeta {}
}

export interface VbenAdminProAppConfigRaw {
  VITE_GLOB_API_URL: string;
  VITE_GLOB_WS_URL: string;
  VITE_GLOB_FILE_SERVER: string;
  VITE_GLOB_CLIENT_SERVER: string;
  VITE_GLOB_CURRENT_ORIGIN: string;
  VITE_GLOB_KKFILEVIEWER_URL: string;
}

export interface ApplicationConfig {
  apiURL: string;
  wsURL: string;
  fileServer: string;
  clientServer: string;
  currentOrigin: string;
  kkFileViewerUrl: string;
}

declare global {
  interface Window {
    _VBEN_ADMIN_PRO_APP_CONF_: VbenAdminProAppConfigRaw;
  }
}
