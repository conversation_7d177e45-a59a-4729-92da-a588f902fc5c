const fileTypeEnum = [
  { name: '文档', code: 1 },
  { name: '图片', code: 2 },
  { name: '音频', code: 3 },
  { name: '视频', code: 4 },
  { name: 'PDF', code: 5 },
];

// MIME 类型映射表
const MIME_TYPE_MAP = {
  // 图片
  'image/png': fileTypeEnum[1],
  'image/jpeg': fileTypeEnum[1],
  'image/gif': fileTypeEnum[1],
  'image/webp': fileTypeEnum[1],
  'image/svg+xml': fileTypeEnum[1],

  // 视频
  'video/mp4': fileTypeEnum[3],
  'video/webm': fileTypeEnum[3],
  'video/ogg': fileTypeEnum[3],

  // 音频
  'audio/mpeg': fileTypeEnum[2],
  'audio/wav': fileTypeEnum[2],
  'audio/ogg': fileTypeEnum[2],
  'audio/mp4': fileTypeEnum[2],
  'audio/mp3': fileTypeEnum[2],

  // 文档
  'text/plain': fileTypeEnum[0],
  'application/msword': fileTypeEnum[0],
  'application/vnd.ms-excel': fileTypeEnum[0],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    fileTypeEnum[0],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
    fileTypeEnum[0],
  'application/vnd.ms-powerpoint': fileTypeEnum[0],
  'application/vnd.openxmlformats-officedocument.presentationml.presentation':
    fileTypeEnum[0],

  // PDF
  'application/pdf': fileTypeEnum[4],
};

/**
 * 根据 MIME 类型获取文件类型
 * @param {string} mimeType - MIME 类型，如 "image/png"
 * @param {number|null} defaultTypeCode - 找不到时返回的默认类型
 * @returns {number|null} - 返回文件类型枚举对象
 */
export function getFileType(mimeType: string, defaultTypeCode = null) {
  return (
    MIME_TYPE_MAP[mimeType as keyof typeof MIME_TYPE_MAP]?.code ||
    defaultTypeCode
  );
}
